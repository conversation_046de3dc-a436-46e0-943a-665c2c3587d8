#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
War Thunder Dataset Management Tool
用于管理和评估多人采集的War Thunder游戏数据集

功能：
1. 数据集质量评估 - 检查图像和标签文件，筛选有效目标
2. 文件重命名 - 按照批次+学号格式重命名图像和标签
3. 数据过滤 - 根据bbox大小要求过滤数据集

作者：MMDetection Team
日期：2025-01-19
"""

import os
import json
import glob
import shutil
import argparse
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image, ImageDraw, ImageFont
from tqdm import tqdm
from collections import Counter, defaultdict
import random

# 配置matplotlib中文字体支持
def setup_chinese_font():
    """设置中文字体支持"""
    import matplotlib.font_manager as fm

    # 尝试不同的中文字体
    chinese_fonts = [
        'SimHei',           # Windows 黑体
        'Microsoft YaHei',  # Windows 微软雅黑
        'DejaVu Sans',      # Linux 默认字体
        'WenQuanYi Micro Hei',  # Linux 文泉驿微米黑
        'Arial Unicode MS', # macOS
        'PingFang SC',      # macOS 苹方
        'Hiragino Sans GB', # macOS 冬青黑体
    ]

    # 查找可用的中文字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    chinese_font = None

    for font in chinese_fonts:
        if font in available_fonts:
            chinese_font = font
            break

    if chinese_font:
        plt.rcParams['font.sans-serif'] = [chinese_font] + plt.rcParams['font.sans-serif']
        print(f"✅ 使用中文字体: {chinese_font}")
    else:
        print("⚠️  警告: 未找到中文字体，可能显示乱码")
        chinese_font = 'DejaVu Sans'  # 使用默认字体

    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    return chinese_font

# 初始化中文字体
CHINESE_FONT = setup_chinese_font()

# 导入通用文件工具
try:
    from universal_file_utils import find_image_label_pairs, detect_file_format, find_all_images, get_matching_label_file
except ImportError:
    print("⚠️  警告: 无法导入universal_file_utils，将使用原始文件查找方法")

class WTDatasetManager:
    """War Thunder数据集管理器"""
    
    def __init__(self, dataset_path):
        """
        初始化数据集管理器
        
        Args:
            dataset_path (str): 数据集根目录路径
        """
        self.dataset_path = dataset_path
        self.stats = {
            'total_images': 0,
            'total_labels': 0,
            'valid_pairs': 0,
            'valid_targets': 0,
            'filtered_images': 0,
            'bbox_stats': defaultdict(int)
        }
        
    def validate_dataset(self):
        """
        任务1: 数据集质量评估
        检查图像和标签文件的配对情况，统计有效目标
        """
        print("=" * 60)
        print("任务1: 数据集质量评估")
        print("=" * 60)
        
        # 使用通用文件查找方法
        try:
            # 检测文件格式
            format_type = detect_file_format(self.dataset_path)
            print(f"检测到文件格式: {format_type}")
            
            # 查找所有配对文件
            all_pairs = find_image_label_pairs(self.dataset_path)
            print(f"发现有效文件对: {len(all_pairs)} 个")
            
            self.stats['total_images'] = len(all_pairs)
            self.stats['total_labels'] = len(all_pairs)
            
        except NameError:
            # 降级到原始方法
            print("使用原始文件查找方法")
            image_files = glob.glob(os.path.join(self.dataset_path, "output_images*.png"))
            all_pairs = []
            for image_file in image_files:
                base_name = os.path.basename(image_file)
                number = base_name.replace("output_images", "").replace(".png", "")
                label_file = os.path.join(self.dataset_path, f"video_label{number}.json")
                if os.path.exists(label_file):
                    all_pairs.append((image_file, label_file))
            
            self.stats['total_images'] = len(all_pairs)
            self.stats['total_labels'] = len(all_pairs)
        
        print(f"发现图像文件: {self.stats['total_images']} 个")
        print(f"发现标签文件: {self.stats['total_labels']} 个")
        
        # 检查配对情况
        valid_pairs = []
        invalid_files = []
        
        for image_file, label_file in tqdm(all_pairs, desc="检查图像标签配对"):
            # 检查是否包含有效目标
            valid_targets = self._count_valid_targets(label_file)
            if valid_targets > 0:
                valid_pairs.append((image_file, label_file, valid_targets))
                self.stats['valid_targets'] += valid_targets
            else:
                invalid_files.append((image_file, label_file, "无有效目标"))
        
        self.stats['valid_pairs'] = len(valid_pairs)
        
        print(f"\n配对检查结果:")
        print(f"有效配对: {self.stats['valid_pairs']} 个")
        print(f"有效目标总数: {self.stats['valid_targets']} 个")
        print(f"无效文件: {len(invalid_files)} 个")
        
        # 显示部分无效文件
        if invalid_files:
            print(f"\n前10个无效文件示例:")
            for i, (img, lbl, reason) in enumerate(invalid_files[:10]):
                img_name = os.path.basename(img) if img else "N/A"
                lbl_name = os.path.basename(lbl) if lbl else "N/A"
                print(f"  {i+1}. 图像: {img_name}, 标签: {lbl_name}, 原因: {reason}")
        
        # 删除无效文件
        if invalid_files:
            response = input(f"\n是否删除 {len(invalid_files)} 个无效文件? (y/n): ")
            if response.lower() == 'y':
                self._remove_invalid_files(invalid_files)
                print("已删除无效文件")
        
        return valid_pairs
    
    def _count_valid_targets(self, label_file):
        """
        统计标签文件中的有效目标数量
        
        Args:
            label_file (str): 标签文件路径
            
        Returns:
            int: 有效目标数量
        """
        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            valid_count = 0
            for unit in data.get('units', []):
                # 筛选条件：必须可见且包含flightModels
                if unit.get('isvisible', False) and "flightModels" in unit.get('m_blk_path', ''):
                    bbox = unit.get('bbox', None)
                    if bbox and any(x != 0 for x in bbox):  # bbox不能全为0
                        valid_count += 1
                        
                        # 统计bbox大小
                        x1, y1, x2, y2 = bbox
                        width = abs(x2 - x1)
                        height = abs(y2 - y1)
                        diagonal = np.sqrt(width**2 + height**2)
                        
                        if diagonal < 33:
                            self.stats['bbox_stats']['small'] += 1
                        elif diagonal <= 90:
                            self.stats['bbox_stats']['medium'] += 1
                        else:
                            self.stats['bbox_stats']['large'] += 1
            
            return valid_count
            
        except Exception as e:
            print(f"读取标签文件失败 {label_file}: {e}")
            return 0
    
    def _remove_invalid_files(self, invalid_files):
        """删除无效文件"""
        for img_file, label_file, reason in invalid_files:
            try:
                if img_file and os.path.exists(img_file):
                    os.remove(img_file)
                if label_file and os.path.exists(label_file):
                    os.remove(label_file)
            except Exception as e:
                print(f"删除文件失败: {e}")
    
    def rename_files(self, batch_id, student_id):
        """
        任务2: 文件重命名
        按照 batch{batch_id}_{student_id}_{序号} 格式重命名
        
        Args:
            batch_id (str): 批次ID
            student_id (str): 学号
        """
        print("=" * 60)
        print("任务2: 文件重命名")
        print("=" * 60)
        
        # 查找所有图像和标签文件
        image_files = glob.glob(os.path.join(self.dataset_path, "output_images*.png"))
        image_files.sort(key=lambda x: int(os.path.basename(x).replace("output_images", "").replace(".png", "")))
        
        print(f"找到 {len(image_files)} 个图像文件待重命名")
        
        renamed_count = 0
        for i, image_file in enumerate(tqdm(image_files, desc="重命名文件")):
            # 提取原始编号
            base_name = os.path.basename(image_file)
            number = base_name.replace("output_images", "").replace(".png", "")
            
            # 检查对应的标签文件
            label_file = os.path.join(self.dataset_path, f"video_label{number}.json")
            
            if os.path.exists(label_file):
                # 新的文件名格式
                new_image_name = f"batch{batch_id}_{student_id}_{i+1}.png"
                new_label_name = f"batch{batch_id}_{student_id}_{i+1}.json"
                
                new_image_path = os.path.join(self.dataset_path, new_image_name)
                new_label_path = os.path.join(self.dataset_path, new_label_name)
                
                try:
                    # 重命名图像文件
                    shutil.move(image_file, new_image_path)
                    # 重命名标签文件
                    shutil.move(label_file, new_label_path)
                    renamed_count += 1
                except Exception as e:
                    print(f"重命名失败 {base_name}: {e}")
        
        print(f"成功重命名 {renamed_count} 对文件")
        print(f"新的命名格式: batch{batch_id}_{student_id}_{{序号}}.{{扩展名}}")
    
    def filter_by_bbox_size(self, max_size=90):
        """
        任务3: 根据bbox大小过滤数据集
        只删除大于max_size的目标，保留所有小于max_size的目标
        
        Args:
            max_size (int): 最大bbox对角线长度，超过此大小的目标将被删除
        """
        print("=" * 60)
        print("任务3: 数据集bbox大小过滤")
        print("=" * 60)
        
        print(f"过滤条件: 删除bbox对角线长度 > {max_size} 像素的图像")
        print(f"保留条件: 保留所有bbox对角线长度 ≤ {max_size} 像素的图像")
        
        # 查找所有图像和标签文件
        image_files = glob.glob(os.path.join(self.dataset_path, "*.png"))
        
        kept_files = []
        removed_files = []
        bbox_size_distribution = defaultdict(int)
        
        for image_file in tqdm(image_files, desc="过滤数据集"):
            # 找到对应的标签文件
            base_name = os.path.basename(image_file).replace(".png", "")
            label_file = os.path.join(self.dataset_path, f"{base_name}.json")
            
            if not os.path.exists(label_file):
                continue
            
            # 检查这个文件是否包含符合大小要求的目标
            has_valid_targets, has_oversized_targets = self._check_bbox_sizes_for_filter(
                label_file, max_size, bbox_size_distribution)
            
            if has_valid_targets and not has_oversized_targets:
                kept_files.append((image_file, label_file))
            else:
                removed_files.append((image_file, label_file))
        
        print(f"\n过滤结果:")
        print(f"保留文件: {len(kept_files)} 对")
        print(f"删除文件: {len(removed_files)} 对 (包含超大目标)")
        
        # 显示bbox大小分布
        print(f"\nBBox大小分布:")
        for size_range, count in sorted(bbox_size_distribution.items()):
            print(f"  {size_range}: {count} 个")
        
        # 删除不符合要求的文件
        if removed_files:
            response = input(f"\n是否删除 {len(removed_files)} 对包含超大目标的文件? (y/n): ")
            if response.lower() == 'y':
                for img_file, lbl_file in removed_files:
                    try:
                        os.remove(img_file)
                        os.remove(lbl_file)
                    except Exception as e:
                        print(f"删除文件失败: {e}")
                print("已删除包含超大目标的文件")
        
        self.stats['filtered_images'] = len(kept_files)
        
        # 生成统计图表
        self._generate_size_distribution_chart(bbox_size_distribution)
    
    def _check_bbox_sizes_for_filter(self, label_file, max_size, size_dist):
        """
        检查标签文件中的bbox大小，用于过滤判断
        
        Args:
            label_file (str): 标签文件路径
            max_size (int): 最大尺寸阈值
            size_dist (dict): 用于统计大小分布
            
        Returns:
            tuple: (has_valid_targets, has_oversized_targets)
        """
        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            has_valid_targets = False
            has_oversized_targets = False
            
            for unit in data.get('units', []):
                # 筛选条件：必须可见且包含flightModels
                if unit.get('isvisible', False) and "flightModels" in unit.get('m_blk_path', ''):
                    bbox = unit.get('bbox', None)
                    if bbox and any(x != 0 for x in bbox):
                        x1, y1, x2, y2 = bbox
                        width = abs(x2 - x1)
                        height = abs(y2 - y1)
                        diagonal = np.sqrt(width**2 + height**2)
                        
                        # 统计分布
                        if diagonal < 10:
                            size_dist['< 10px'] += 1
                        elif diagonal < 33:
                            size_dist['10-33px'] += 1
                        elif diagonal <= 90:
                            size_dist['33-90px'] += 1
                        else:
                            size_dist['> 90px'] += 1
                        
                        has_valid_targets = True
                        
                        # 检查是否有超大目标
                        if diagonal > max_size:
                            has_oversized_targets = True
            
            return has_valid_targets, has_oversized_targets
            
        except Exception as e:
            print(f"读取标签文件失败 {label_file}: {e}")
            return False, False
    
    def _generate_size_distribution_chart(self, size_distribution):
        """生成bbox大小分布图表"""
        if not size_distribution:
            return
        
        plt.figure(figsize=(10, 6))
        sizes = list(size_distribution.keys())
        counts = list(size_distribution.values())
        
        colors = ['red', 'green', 'blue', 'orange', 'purple'][:len(sizes)]
        plt.bar(sizes, counts, color=colors)
        plt.title("War Thunder数据集 - BBox大小分布", fontproperties=CHINESE_FONT)
        plt.xlabel("BBox对角线长度范围", fontproperties=CHINESE_FONT)
        plt.ylabel("目标数量", fontproperties=CHINESE_FONT)
        plt.xticks(rotation=15)

        # 添加数值标签
        for i, count in enumerate(counts):
            plt.text(i, count + max(counts) * 0.02, str(count), ha='center', fontproperties=CHINESE_FONT)
        
        # 保存图表
        chart_path = os.path.join(self.dataset_path, "bbox_size_distribution.png")
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"已保存大小分布图表到: {chart_path}")
    
    def generate_summary_report(self):
        """生成数据集处理总结报告"""
        print("=" * 60)
        print("数据集处理总结报告")
        print("=" * 60)
        
        print(f"原始图像文件数: {self.stats['total_images']}")
        print(f"原始标签文件数: {self.stats['total_labels']}")
        print(f"有效配对数: {self.stats['valid_pairs']}")
        print(f"有效目标总数: {self.stats['valid_targets']}")
        print(f"最终保留图像数: {self.stats['filtered_images']}")
        
        print(f"\nBBox大小分布:")
        for size_type, count in self.stats['bbox_stats'].items():
            print(f"  {size_type}: {count} 个")
        
        # 保存报告到文件
        report_path = os.path.join(self.dataset_path, "dataset_summary_report.txt")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("War Thunder数据集处理总结报告\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"处理时间: {__import__('datetime').datetime.now()}\n")
            f.write(f"数据集路径: {self.dataset_path}\n\n")
            f.write(f"原始图像文件数: {self.stats['total_images']}\n")
            f.write(f"原始标签文件数: {self.stats['total_labels']}\n")
            f.write(f"有效配对数: {self.stats['valid_pairs']}\n")
            f.write(f"有效目标总数: {self.stats['valid_targets']}\n")
            f.write(f"最终保留图像数: {self.stats['filtered_images']}\n\n")
            f.write("BBox大小分布:\n")
            for size_type, count in self.stats['bbox_stats'].items():
                f.write(f"  {size_type}: {count} 个\n")
        
        print(f"\n已保存处理报告到: {report_path}")
    
    def visualize_bbox_samples(self, num_samples=20, seed=42):
        """
        任务4: 可视化bbox样本
        随机选择不同大小的目标进行可视化
        
        Args:
            num_samples (int): 每个大小类别的样本数量
            seed (int): 随机种子
        """
        print("=" * 60)
        print("任务4: BBox可视化")
        print("=" * 60)
        
        # 设置随机种子
        random.seed(seed)
        np.random.seed(seed)
        
        # 创建可视化输出目录
        vis_dir = os.path.join(self.dataset_path, "bbox_visualization")
        os.makedirs(vis_dir, exist_ok=True)
        
        # 收集不同大小的样本
        size_categories = {
            'tiny': {'range': '< 10px', 'samples': [], 'color': 'red'},
            'small': {'range': '10-33px', 'samples': [], 'color': 'blue'}, 
            'medium': {'range': '33-90px', 'samples': [], 'color': 'green'},
            'large': {'range': '> 90px', 'samples': [], 'color': 'orange'}
        }
        
        # 使用通用文件查找方法
        try:
            all_pairs = find_image_label_pairs(self.dataset_path)
            print(f"找到 {len(all_pairs)} 对文件")
        except NameError:
            # 降级到原始方法
            image_files = glob.glob(os.path.join(self.dataset_path, "output_images*.png"))
            all_pairs = []
            for image_file in image_files:
                base_name = os.path.basename(image_file)
                number = base_name.replace("output_images", "").replace(".png", "")
                label_file = os.path.join(self.dataset_path, f"video_label{number}.json")
                if os.path.exists(label_file):
                    all_pairs.append((image_file, label_file))
        
        print("正在收集不同大小的目标样本...")
        for image_file, label_file in tqdm(all_pairs):
            
            # 收集这个文件中的有效目标
            targets = self._collect_valid_targets(image_file, label_file)
            
            # 按大小分类
            for target in targets:
                diagonal = target['diagonal']
                if diagonal < 10:
                    category = 'tiny'
                elif diagonal < 33:
                    category = 'small'
                elif diagonal <= 90:
                    category = 'medium'
                else:
                    category = 'large'
                
                if len(size_categories[category]['samples']) < num_samples * 2:  # 收集更多样本以便随机选择
                    size_categories[category]['samples'].append(target)
        
        # 为每个类别随机选择样本并可视化
        print("\n正在生成可视化图像...")
        total_visualized = 0
        
        for category, info in size_categories.items():
            samples = info['samples']
            if not samples:
                print(f"⚠️  {info['range']} 类别没有找到样本")
                continue
            
            # 随机选择样本
            selected_samples = random.sample(samples, min(num_samples, len(samples)))
            print(f"📊 {info['range']} 类别: 找到 {len(samples)} 个样本, 可视化 {len(selected_samples)} 个")
            
            # 为每个样本生成可视化
            for i, sample in enumerate(selected_samples):
                try:
                    self._visualize_single_sample(sample, category, i+1, vis_dir, info['color'])
                    total_visualized += 1
                except Exception as e:
                    print(f"可视化失败 {sample['image_file']}: {e}")
        
        # 生成总览图
        self._generate_overview_visualization(size_categories, vis_dir, num_samples)
        
        print(f"\n✅ 可视化完成!")
        print(f"📁 输出目录: {vis_dir}")
        print(f"📊 总计可视化: {total_visualized} 个样本")
        print(f"📋 文件列表:")
        print(f"   - overview_bbox_samples.png (总览图)")
        for category, info in size_categories.items():
            if info['samples']:
                print(f"   - {category}_*.png ({info['range']} 样本)")
    
    def _collect_valid_targets(self, image_file, label_file):
        """收集图像中的有效目标信息"""
        targets = []
        
        try:
            # 读取图像尺寸
            img = Image.open(image_file)
            img_width, img_height = img.size
            
            # 读取标签
            with open(label_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for unit in data.get('units', []):
                # 筛选条件：必须可见且包含flightModels
                if unit.get('isvisible', False) and "flightModels" in unit.get('m_blk_path', ''):
                    bbox = unit.get('bbox', None)
                    if bbox and any(x != 0 for x in bbox):
                        x1, y1, x2, y2 = bbox
                        width = abs(x2 - x1)
                        height = abs(y2 - y1)
                        diagonal = np.sqrt(width**2 + height**2)
                        
                        # 检查bbox是否在图像范围内
                        if (0 <= x1 < img_width and 0 <= y1 < img_height and 
                            0 <= x2 < img_width and 0 <= y2 < img_height and
                            x1 < x2 and y1 < y2):
                            
                            targets.append({
                                'image_file': image_file,
                                'label_file': label_file,
                                'bbox': bbox,
                                'diagonal': diagonal,
                                'width': width,
                                'height': height,
                                'img_width': img_width,
                                'img_height': img_height,
                                'aircraft_name': unit.get('m_short_name', 'Unknown'),
                                'aircraft_type': unit.get('m_vehicle_type_short', 'Aircraft')
                            })
                        else:
                            # 跳过超出图像范围的bbox
                            continue
        
        except Exception as e:
            print(f"收集目标失败 {image_file}: {e}")
        
        return targets
    
    def _visualize_single_sample(self, sample, category, index, vis_dir, color):
        """可视化单个样本"""
        # 读取图像
        img = Image.open(sample['image_file'])
        
        # 创建matplotlib图形
        fig, ax = plt.subplots(1, 1, figsize=(10, 8))
        ax.imshow(img)
        
        # 绘制bbox
        x1, y1, x2, y2 = sample['bbox']
        width = abs(x2 - x1)
        height = abs(y2 - y1)
        
        # 创建矩形框
        rect = patches.Rectangle((x1, y1), width, height, 
                               linewidth=3, edgecolor=color, facecolor='none')
        ax.add_patch(rect)
        
        # 添加标签信息
        label_text = f"{sample['aircraft_name']}\n{sample['diagonal']:.1f}px"
        ax.text(x1, y1-10, label_text, fontsize=12, color=color,
               bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8),
               fontproperties=CHINESE_FONT)

        # 设置标题
        ax.set_title(f"{category.upper()} Target Sample {index}\n"
                    f"Size: {sample['width']:.0f}x{sample['height']:.0f} "
                    f"(diagonal: {sample['diagonal']:.1f}px)",
                    fontsize=14, fontweight='bold', fontproperties=CHINESE_FONT)
        
        ax.axis('off')
        
        # 保存图像
        output_file = os.path.join(vis_dir, f"{category}_{index:02d}_sample.png")
        plt.savefig(output_file, dpi=150, bbox_inches='tight')
        plt.close()
    
    def _generate_overview_visualization(self, size_categories, vis_dir, num_samples):
        """生成总览可视化图"""
        # 统计每个类别的样本数量
        category_counts = {}
        sample_images = {}
        
        for category, info in size_categories.items():
            category_counts[category] = len(info['samples'])
            # 选择一个代表性样本用于总览
            if info['samples']:
                sample_images[category] = random.choice(info['samples'])
        
        # 创建总览图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('War Thunder数据集 - BBox大小分布总览',
                    fontsize=16, fontweight='bold', fontproperties=CHINESE_FONT)
        
        categories = ['tiny', 'small', 'medium', 'large']
        positions = [(0,0), (0,1), (1,0), (1,1)]
        
        for i, category in enumerate(categories):
            row, col = positions[i]
            ax = axes[row, col]
            
            if category in sample_images:
                sample = sample_images[category]
                img = Image.open(sample['image_file'])
                ax.imshow(img)
                
                # 绘制bbox
                x1, y1, x2, y2 = sample['bbox']
                width = abs(x2 - x1)
                height = abs(y2 - y1)
                
                rect = patches.Rectangle((x1, y1), width, height, 
                                       linewidth=4, 
                                       edgecolor=size_categories[category]['color'], 
                                       facecolor='none')
                ax.add_patch(rect)
                
                title = f"{category.upper()} ({size_categories[category]['range']})\n"
                title += f"Samples: {category_counts[category]}, "
                title += f"Size: {sample['diagonal']:.1f}px"
            else:
                ax.text(0.5, 0.5, f"No {category.upper()} samples\n({size_categories[category]['range']})",
                       ha='center', va='center', transform=ax.transAxes, fontsize=12,
                       fontproperties=CHINESE_FONT)
                title = f"{category.upper()} ({size_categories[category]['range']})\nNo samples found"

            ax.set_title(title, fontsize=12, fontweight='bold', fontproperties=CHINESE_FONT)
            ax.axis('off')
        
        plt.tight_layout()
        overview_file = os.path.join(vis_dir, "overview_bbox_samples.png")
        plt.savefig(overview_file, dpi=200, bbox_inches='tight')
        plt.close()
        
        print(f"📊 总览图已保存: {overview_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="War Thunder数据集管理工具")
    parser.add_argument("--dataset_path", type=str, default="D:/WTdataset", 
                       help="数据集路径 (默认: D:/WTdataset)")
    parser.add_argument("--batch_id", type=str, help="批次ID (用于重命名)")
    parser.add_argument("--student_id", type=str, help="学号 (用于重命名)")
    parser.add_argument("--task", type=str, choices=['validate', 'rename', 'filter', 'visualize', 'all'], 
                       default='all', help="执行的任务类型")
    parser.add_argument("--max_size", type=int, default=90, 
                       help="最大bbox对角线长度，超过此大小将被删除 (默认: 90)")
    parser.add_argument("--vis_samples", type=int, default=10, 
                       help="每个大小类别的可视化样本数量 (默认: 10)")
    
    args = parser.parse_args()
    
    # 交互式输入缺失的参数
    if not args.dataset_path or not os.path.exists(args.dataset_path):
        dataset_path = input("请输入数据集路径 (默认: D:/WTdataset): ").strip()
        if not dataset_path:
            dataset_path = "D:/WTdataset"
        # 转换为WSL路径格式
        if dataset_path.startswith("D:"):
            dataset_path = dataset_path.replace("D:", "/mnt/d")
        elif dataset_path.startswith("E:"):
            dataset_path = dataset_path.replace("E:", "/mnt/e")
        args.dataset_path = dataset_path.replace("\\", "/")
    
    if not os.path.exists(args.dataset_path):
        print(f"错误: 数据集路径不存在: {args.dataset_path}")
        return
    
    print(f"使用数据集路径: {args.dataset_path}")
    
    # 创建数据集管理器
    manager = WTDatasetManager(args.dataset_path)
    
    # 执行任务
    if args.task in ['validate', 'all']:
        valid_pairs = manager.validate_dataset()
    
    if args.task in ['rename', 'all']:
        if not args.batch_id:
            args.batch_id = input("请输入批次ID: ").strip()
        if not args.student_id:
            args.student_id = input("请输入学号: ").strip()
        
        if args.batch_id and args.student_id:
            manager.rename_files(args.batch_id, args.student_id)
        else:
            print("跳过重命名任务 (缺少批次ID或学号)")
    
    if args.task in ['filter', 'all']:
        manager.filter_by_bbox_size(args.max_size)
    
    if args.task in ['visualize', 'all']:
        manager.visualize_bbox_samples(args.vis_samples)
    
    # 生成总结报告
    manager.generate_summary_report()
    
    print("\n数据集处理完成!")

if __name__ == "__main__":
    main() 