#!/bin/bash

echo "=========================================="
echo "    War Thunder数据集管理工具"
echo "=========================================="
echo ""

# 检测Python版本
echo "🔍 检测Python环境..."

# 尝试python3
if command -v python3 &> /dev/null; then
    echo "✅ 检测到python3"
    echo "🚀 正在启动工具..."
    python3 quick_start.py
elif command -v python &> /dev/null; then
    echo "✅ 检测到python"
    echo "🚀 正在启动工具..."
    python quick_start.py
else
    echo "❌ 错误: 未检测到Python环境"
    echo ""
    echo "请确保已安装Python并添加到PATH"
    echo "在Ubuntu/Debian上: sudo apt install python3"
    echo "在macOS上: brew install python3"
    echo ""
    read -p "按回车键退出..."
    exit 1
fi

echo ""
echo "🎉 工具执行完成"
read -p "按回车键退出..." 