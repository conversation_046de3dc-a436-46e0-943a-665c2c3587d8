#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
红外小目标检测器 v2.0 测试脚本
用于验证模型是否能正常创建和运行
"""

import torch
import numpy as np
from infrared_detector2 import (
    InfraredSmallTargetDetector,
    InfraredEnhancementModule,
    FPNBackbone,
    TaskAlignedHead,
    QualityFocalLoss
)

def test_model_creation():
    """测试模型创建"""
    print("🔧 测试模型创建...")
    
    try:
        # 创建模型
        model = InfraredSmallTargetDetector(num_classes=1, input_size=640)
        print("✅ 模型创建成功")
        
        # 打印模型参数数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"📊 模型参数统计:")
        print(f"   总参数数: {total_params:,}")
        print(f"   可训练参数: {trainable_params:,}")
        
        return model
        
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        return None

def test_forward_pass(model):
    """测试前向传播"""
    print("\n🚀 测试前向传播...")
    
    try:
        # 创建随机输入
        batch_size = 2
        input_tensor = torch.randn(batch_size, 3, 640, 640)
        
        # 设置为评估模式
        model.eval()
        
        with torch.no_grad():
            # 前向传播
            outputs = model(input_tensor)
            
            print("✅ 前向传播成功")
            print(f"📊 输出统计:")
            print(f"   输出数量: {len(outputs)}")
            
            for i, output in enumerate(outputs):
                print(f"   层 {i+1}:")
                print(f"     boxes shape: {output['boxes'].shape}")
                print(f"     scores shape: {output['scores'].shape}")
                print(f"     labels shape: {output['labels'].shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_modules():
    """测试各个模块"""
    print("\n🔍 测试各个模块...")
    
    # 测试红外增强模块
    try:
        enhancement = InfraredEnhancementModule(in_channels=3)
        test_input = torch.randn(1, 3, 256, 256)
        enhanced = enhancement(test_input)
        print("✅ 红外增强模块测试通过")
        print(f"   输入形状: {test_input.shape}")
        print(f"   输出形状: {enhanced.shape}")
    except Exception as e:
        print(f"❌ 红外增强模块测试失败: {e}")
    
    # 测试FPN骨干网络
    try:
        backbone = FPNBackbone(in_channels=3)
        test_input = torch.randn(1, 3, 256, 256)
        features = backbone(test_input)
        print("✅ FPN骨干网络测试通过")
        print(f"   输入形状: {test_input.shape}")
        print(f"   输出特征数: {len(features)}")
        for i, feat in enumerate(features):
            print(f"   特征 {i+1} 形状: {feat.shape}")
    except Exception as e:
        print(f"❌ FPN骨干网络测试失败: {e}")
    
    # 测试检测头
    try:
        head = TaskAlignedHead(in_channels=256, num_classes=1)
        test_features = [torch.randn(1, 256, 64, 64) for _ in range(4)]
        outputs = head(test_features)
        print("✅ 检测头测试通过")
        print(f"   输入特征数: {len(test_features)}")
        print(f"   输出数: {len(outputs)}")
    except Exception as e:
        print(f"❌ 检测头测试失败: {e}")
    
    # 测试损失函数
    try:
        qfl = QualityFocalLoss()
        pred = torch.randn(10, 2)  # 10个样本，2个类别
        target = torch.randint(0, 2, (10,))  # 随机标签
        quality = torch.rand(10)  # 质量分数
        loss = qfl(pred, target, quality)
        print("✅ 质量感知损失测试通过")
        print(f"   损失值: {loss.item():.4f}")
    except Exception as e:
        print(f"❌ 质量感知损失测试失败: {e}")

def test_gpu_compatibility():
    """测试GPU兼容性"""
    print("\n🖥️ 测试GPU兼容性...")
    
    if torch.cuda.is_available():
        try:
            device = torch.device('cuda')
            model = InfraredSmallTargetDetector(num_classes=1, input_size=640)
            model = model.to(device)
            
            # 测试GPU前向传播
            input_tensor = torch.randn(1, 3, 640, 640).to(device)
            model.eval()
            
            with torch.no_grad():
                outputs = model(input_tensor)
            
            print("✅ GPU兼容性测试通过")
            print(f"   GPU设备: {torch.cuda.get_device_name()}")
            print(f"   显存使用: {torch.cuda.memory_allocated() / 1024**2:.1f} MB")
            
        except Exception as e:
            print(f"❌ GPU兼容性测试失败: {e}")
    else:
        print("⚠️ CUDA不可用，跳过GPU测试")

def main():
    """主测试函数"""
    print("🎯 红外小目标检测器 v2.0 - 模型测试")
    print("=" * 50)
    
    # 测试模型创建
    model = test_model_creation()
    if model is None:
        print("❌ 模型创建失败，终止测试")
        return
    
    # 测试前向传播
    if not test_forward_pass(model):
        print("❌ 前向传播失败，终止测试")
        return
    
    # 测试各个模块
    test_individual_modules()
    
    # 测试GPU兼容性
    test_gpu_compatibility()
    
    print("\n🎉 所有测试完成!")
    print("✅ 模型可以正常创建和运行")
    print("💡 现在可以开始训练或推理了")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
