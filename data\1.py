import torch

def check_gpu_info():
    print("=" * 50)
    print("GPU & CUDA 信息检查")
    print("=" * 50)

    # 1. 检查 CUDA 是否可用
    cuda_available = torch.cuda.is_available()
    print(f"✅ CUDA 是否可用: {cuda_available}")

    if cuda_available:
        # 2. 获取 CUDA 版本
        print(f"📌 CUDA 版本: {torch.version.cuda}")

        # 3. 获取 cuDNN 版本
        print(f"📌 cuDNN 版本: {torch.backends.cudnn.version()}")

        # 4. 检查当前 GPU 设备信息
        current_device = torch.cuda.current_device()
        gpu_name = torch.cuda.get_device_name(current_device)
        print(f"🖥️ 当前 GPU: {gpu_name}")
        
        # 5. 检查 GPU 数量
        gpu_count = torch.cuda.device_count()
        print(f"🔢 可用 GPU 数量: {gpu_count}")
        
        # 6. 检查当前 GPU 内存使用情况
        total_mem = torch.cuda.get_device_properties(current_device).total_memory / 1024**3  # 转换为 GB
        print(f"🧠 GPU 显存总量: {total_mem:.2f} GB")
    else:
        print("❌ 未检测到可用的 CUDA 设备")

if __name__ == "__main__":
    check_gpu_info()