#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
War Thunder数据集管理工具演示脚本
展示完整的数据集处理流程

使用方法:
python run_wt_dataset_demo.py --dataset_path /path/to/dataset --batch_id 1 --student_id 202301
"""

import os
import sys
import argparse

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wt_dataset_manager import WTDatasetManager

def demo_full_pipeline(dataset_path, batch_id=None, student_id=None, 
                      min_size=33, max_size=90, test_mode=False):
    """
    演示完整的数据集处理流程
    
    Args:
        dataset_path (str): 数据集路径
        batch_id (str): 批次ID
        student_id (str): 学号
        min_size (int): 最小bbox大小
        max_size (int): 最大bbox大小
        test_mode (bool): 测试模式，不执行实际的文件操作
    """
    print("War Thunder数据集管理工具 - 完整演示")
    print("=" * 60)
    print(f"数据集路径: {dataset_path}")
    print(f"批次ID: {batch_id}")
    print(f"学号: {student_id}")
    print(f"BBox大小过滤范围: {min_size}-{max_size} 像素")
    print(f"测试模式: {'是' if test_mode else '否'}")
    print("=" * 60)
    
    if not os.path.exists(dataset_path):
        print(f"错误: 数据集路径不存在: {dataset_path}")
        return
    
    # 创建数据集管理器
    manager = WTDatasetManager(dataset_path)
    
    # 任务1: 数据集质量评估
    print("\n正在执行任务1: 数据集质量评估...")
    valid_pairs = manager.validate_dataset()
    
    if not valid_pairs:
        print("错误: 没有找到有效的图像-标签配对")
        return
    
    # 任务2: 文件重命名 (只在提供了批次和学号时执行)
    if batch_id and student_id and not test_mode:
        print(f"\n正在执行任务2: 文件重命名...")
        response = input(f"确认要将所有文件重命名为batch{batch_id}_{student_id}_xxx格式吗? (y/n): ")
        if response.lower() == 'y':
            manager.rename_files(batch_id, student_id)
        else:
            print("跳过文件重命名")
    elif test_mode:
        print(f"\n[测试模式] 任务2: 文件重命名")
        print(f"将重命名为格式: batch{batch_id}_{student_id}_{{序号}}.{{扩展名}}")
    else:
        print("\n跳过任务2: 文件重命名 (缺少批次ID或学号)")
    
    # 任务3: 数据过滤
    if not test_mode:
        print(f"\n正在执行任务3: 数据过滤...")
        manager.filter_by_bbox_size(min_size, max_size)
    else:
        print(f"\n[测试模式] 任务3: 数据过滤")
        print(f"将过滤bbox大小不在 {min_size}-{max_size} 像素范围内的图像")
    
    # 生成总结报告
    print(f"\n正在生成总结报告...")
    manager.generate_summary_report()
    
    print("\n" + "=" * 60)
    print("数据集处理流程演示完成!")
    print("=" * 60)

def interactive_mode():
    """交互式模式"""
    print("War Thunder数据集管理工具 - 交互模式")
    print("=" * 50)
    
    # 获取数据集路径
    dataset_path = input("请输入数据集路径 (默认: /mnt/e/WTdataset/WTdataset): ").strip()
    if not dataset_path:
        dataset_path = "/mnt/e/WTdataset/WTdataset"
    
    # 转换Windows路径到WSL格式
    if dataset_path.startswith("E:"):
        dataset_path = dataset_path.replace("E:", "/mnt/e").replace("\\", "/")
    elif dataset_path.startswith("D:"):
        dataset_path = dataset_path.replace("D:", "/mnt/d").replace("\\", "/")
    
    if not os.path.exists(dataset_path):
        print(f"错误: 数据集路径不存在: {dataset_path}")
        return
    
    # 选择任务
    print("\n可选任务:")
    print("1. 仅质量评估")
    print("2. 质量评估 + 文件重命名")
    print("3. 质量评估 + bbox大小过滤")
    print("4. 完整流程 (评估 + 重命名 + 过滤)")
    print("5. 测试模式 (仅显示操作，不执行)")
    
    choice = input("\n请选择任务 (1-5): ").strip()
    
    batch_id = None
    student_id = None
    min_size = 33
    max_size = 90
    test_mode = False
    
    if choice in ['2', '4', '5']:
        batch_id = input("请输入批次ID: ").strip()
        student_id = input("请输入学号: ").strip()
    
    if choice in ['3', '4', '5']:
        min_size_input = input(f"请输入最小bbox大小 (默认: {min_size}): ").strip()
        if min_size_input:
            min_size = int(min_size_input)
        
        max_size_input = input(f"请输入最大bbox大小 (默认: {max_size}): ").strip()
        if max_size_input:
            max_size = int(max_size_input)
    
    if choice == '5':
        test_mode = True
    
    # 执行选择的任务
    if choice == '1':
        manager = WTDatasetManager(dataset_path)
        manager.validate_dataset()
        manager.generate_summary_report()
    else:
        demo_full_pipeline(dataset_path, batch_id, student_id, min_size, max_size, test_mode)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="War Thunder数据集管理工具演示")
    parser.add_argument("--dataset_path", type=str, default="/mnt/e/WTdataset/WTdataset",
                       help="数据集路径")
    parser.add_argument("--batch_id", type=str, help="批次ID")
    parser.add_argument("--student_id", type=str, help="学号")
    parser.add_argument("--min_size", type=int, default=33, help="最小bbox大小")
    parser.add_argument("--max_size", type=int, default=90, help="最大bbox大小")
    parser.add_argument("--test_mode", action="store_true", help="测试模式")
    parser.add_argument("--interactive", action="store_true", help="交互式模式")
    
    args = parser.parse_args()
    
    if args.interactive:
        interactive_mode()
    else:
        demo_full_pipeline(
            args.dataset_path,
            args.batch_id,
            args.student_id,
            args.min_size,
            args.max_size,
            args.test_mode
        )

if __name__ == "__main__":
    main() 