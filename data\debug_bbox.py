#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本：检查bbox和可见性匹配问题
"""

import os
import json
import glob
import numpy as np
from PIL import Image

def debug_bbox_visibility(dataset_path="/mnt/e/WTdataset/WTdataset", num_samples=5):
    """调试bbox和可见性匹配问题"""
    print("🔍 调试BBox和可见性匹配问题")
    print("=" * 60)
    
    # 使用通用文件查找方法
    try:
        from universal_file_utils import find_image_label_pairs
        all_pairs = find_image_label_pairs(dataset_path)[:num_samples]
        print(f"找到 {len(all_pairs)} 对文件用于调试")
    except ImportError:
        # 降级到原始方法
        image_files = glob.glob(os.path.join(dataset_path, "output_images*.png"))[:num_samples]
        all_pairs = []
        for image_file in image_files:
            base_name = os.path.basename(image_file)
            number = base_name.replace("output_images", "").replace(".png", "")
            label_file = os.path.join(dataset_path, f"video_label{number}.json")
            if os.path.exists(label_file):
                all_pairs.append((image_file, label_file))
    
    for image_file, label_file in all_pairs:
        base_name = os.path.basename(image_file)
        print(f"\n📁 文件: {base_name}")
        print("-" * 40)
        
        # 读取图像尺寸
        img = Image.open(image_file)
        img_width, img_height = img.size
        print(f"图像尺寸: {img_width} x {img_height}")
        
        # 读取标签
        with open(label_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        total_units = len(data.get('units', []))
        visible_flight_models = 0
        visible_with_bbox = 0
        bbox_issues = []
        
        print(f"总目标数: {total_units}")
        
        for i, unit in enumerate(data.get('units', [])):
            # 检查是否是可见的飞行模型
            is_visible = unit.get('isvisible', False)
            has_flight_models = "flightModels" in unit.get('m_blk_path', '')
            
            if is_visible and has_flight_models:
                visible_flight_models += 1
                bbox = unit.get('bbox', None)
                aircraft_name = unit.get('m_short_name', 'Unknown')
                
                print(f"  ✈️  目标 {i}: {aircraft_name}")
                print(f"      可见: {is_visible}")
                print(f"      路径: {unit.get('m_blk_path', 'N/A')}")
                
                if bbox:
                    x1, y1, x2, y2 = bbox
                    width = abs(x2 - x1)
                    height = abs(y2 - y1)
                    diagonal = np.sqrt(width**2 + height**2)
                    
                    print(f"      BBox: [{x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f}]")
                    print(f"      尺寸: {width:.1f} x {height:.1f} (对角线: {diagonal:.1f}px)")
                    
                    # 检查bbox是否在图像范围内
                    if x1 < 0 or y1 < 0 or x2 > img_width or y2 > img_height:
                        bbox_issues.append(f"目标{i}的bbox超出图像范围")
                        print(f"      ⚠️  bbox超出图像范围!")
                    
                    # 检查bbox是否有效
                    if any(x == 0 for x in bbox) and not all(x == 0 for x in bbox):
                        bbox_issues.append(f"目标{i}的bbox部分为0")
                        print(f"      ⚠️  bbox部分为0!")
                    elif all(x == 0 for x in bbox):
                        bbox_issues.append(f"目标{i}的bbox全为0")
                        print(f"      ❌ bbox全为0!")
                    else:
                        visible_with_bbox += 1
                        print(f"      ✅ bbox有效")
                else:
                    bbox_issues.append(f"目标{i}没有bbox信息")
                    print(f"      ❌ 没有bbox信息!")
        
        print(f"\n📊 统计:")
        print(f"  可见飞行模型: {visible_flight_models}")
        print(f"  有效bbox: {visible_with_bbox}")
        print(f"  问题数量: {len(bbox_issues)}")
        
        if bbox_issues:
            print(f"\n⚠️  发现的问题:")
            for issue in bbox_issues:
                print(f"    - {issue}")

def check_coordinate_system(dataset_path="/mnt/e/WTdataset/WTdataset"):
    """检查坐标系统"""
    print("\n🎯 检查坐标系统")
    print("=" * 60)
    
    try:
        from universal_file_utils import find_image_label_pairs
        all_pairs = find_image_label_pairs(dataset_path)[:3]
    except ImportError:
        # 降级到原始方法
        image_files = glob.glob(os.path.join(dataset_path, "output_images*.png"))[:3]
        all_pairs = []
        for image_file in image_files:
            base_name = os.path.basename(image_file)
            number = base_name.replace("output_images", "").replace(".png", "")
            label_file = os.path.join(dataset_path, f"video_label{number}.json")
            if os.path.exists(label_file):
                all_pairs.append((image_file, label_file))
    
    for image_file, label_file in all_pairs:
        base_name = os.path.basename(image_file)
            
        print(f"\n📁 {base_name}")
        
        # 读取图像尺寸
        img = Image.open(image_file)
        img_width, img_height = img.size
        print(f"图像尺寸: {img_width} x {img_height}")
        
        # 读取标签
        with open(label_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 查找有bbox的目标
        for unit in data.get('units', []):
            bbox = unit.get('bbox', None)
            if bbox and any(x != 0 for x in bbox):
                x1, y1, x2, y2 = bbox
                print(f"  BBox示例: [{x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f}]")
                print(f"  相对位置: 左上({x1/img_width:.2%}, {y1/img_height:.2%}) "
                      f"右下({x2/img_width:.2%}, {y2/img_height:.2%})")
                break

if __name__ == "__main__":
    debug_bbox_visibility()
    check_coordinate_system() 