#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
War Thunder数据集管理工具 - 根目录启动脚本

这个脚本放在项目根目录，可以直接运行而不需要切换目录
"""

import os
import sys
import subprocess

def main():
    """主函数"""
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(script_dir, "data")
    
    # 检查data目录是否存在
    if not os.path.exists(data_dir):
        print("❌ 错误: 找不到data目录")
        print(f"请确保在正确的项目目录下运行此脚本")
        return 1
    
    # 检查工具脚本是否存在
    tool_script = os.path.join(data_dir, "quick_start.py")
    if not os.path.exists(tool_script):
        print("❌ 错误: 找不到quick_start.py")
        print(f"请确保工具文件存在: {tool_script}")
        return 1
    
    print("🎮 War Thunder数据集管理工具")
    print("=" * 50)
    print(f"工具位置: {tool_script}")
    print("正在启动...")
    print("=" * 50)
    
    # 切换到data目录并运行工具
    try:
        os.chdir(data_dir)
        subprocess.run([sys.executable, "quick_start.py"], check=True)
        return 0
    except subprocess.CalledProcessError as e:
        print(f"❌ 运行失败: {e}")
        return 1
    except KeyboardInterrupt:
        print("\n👋 用户中断，再见!")
        return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 