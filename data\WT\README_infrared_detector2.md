# 红外小目标检测器 v2.0 - 深度学习版本

## 🎯 项目简介

本项目是基于FCOS、GFL、TOOD等先进算法的红外小目标检测深度学习实现。使用PyTorch框架，针对红外小目标检测进行专门优化，支持GPU加速训练和推理。

## 🚀 核心特色

### ✅ 深度学习实现
- 基于PyTorch深度学习框架
- 支持CUDA GPU加速训练和推理
- 混合精度训练，提高训练效率
- 完整的训练、验证、测试流程

### 🎨 算法创新点
- **红外图像增强模块**: 针对红外图像特性的预处理网络
- **多尺度特征金字塔网络 (FPN)**: 基于FCOS的改进特征提取
- **质量感知分类损失 (QFL)**: 基于GFL的质量评估策略
- **任务对齐学习 (TAL)**: 基于TOOD的对齐策略
- **小目标检测优化**: 多尺度训练和推理策略

### 📊 完整功能
- 模型训练和验证
- 单张图像推理
- 批量图像推理
- 模型评估和可视化
- 训练过程监控

## 📁 文件结构

```
WT/
├── infrared_detector2.py           # 主程序（深度学习版本）
├── test_infrared_detector2.py      # 模型测试脚本
├── README_infrared_detector2.md    # 本文档
├── checkpoints/                    # 模型检查点目录
├── training_curves.png             # 训练曲线图
└── inference_result.png            # 推理结果图
```

## 🛠️ 环境要求

### 硬件要求
- **GPU**: NVIDIA GPU (推荐4GB+显存)
- **内存**: 8GB以上
- **存储**: 5GB可用空间

### 软件要求
- **Python**: 3.8+
- **CUDA**: 11.8+ (与您的环境匹配)
- **PyTorch**: 2.0+

### 依赖安装
```bash
# 安装PyTorch (根据您的CUDA版本)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装其他依赖
pip install opencv-python pillow matplotlib tqdm numpy
```

## 📖 使用方法

### 1. 快速开始
```bash
cd WT
python infrared_detector2.py
```

### 2. 模型测试
```bash
# 测试模型是否能正常创建和运行
python test_infrared_detector2.py
```

### 3. 训练模型
1. 运行主程序选择"开始训练模型"
2. 输入数据集路径
3. 程序将自动开始训练
4. 训练完成后模型保存在`checkpoints/`目录

### 4. 推理测试
1. 确保已有训练好的模型
2. 选择"单张图像推理"或"批量图像推理"
3. 输入图像路径进行检测

## 🔧 配置说明

### 训练配置
```python
config = {
    'epochs': 50,           # 训练轮数
    'batch_size': 4,        # 批大小（适合4GB显存）
    'learning_rate': 1e-4,  # 学习率
    'weight_decay': 1e-4,   # 权重衰减
    'input_size': 640,      # 输入图像尺寸
    'num_classes': 1        # 类别数（飞机）
}
```

### 模型结构
- **输入**: RGB图像 (3, 640, 640)
- **骨干网络**: 改进的FPN结构
- **检测头**: 任务对齐检测头
- **输出**: 边界框、置信度、类别

## 📊 性能特点

### 优势
- ✅ 深度学习端到端训练
- ✅ GPU加速，训练和推理速度快
- ✅ 融合多种先进算法优势
- ✅ 针对小目标检测优化
- ✅ 支持混合精度训练

### 适用场景
- 红外图像小目标检测
- 军事目标识别
- 无人机检测
- 卫星图像分析

## 🎯 算法原理

### 1. 红外图像增强模块
- 对比度增强分支：自适应调整图像对比度
- 边缘增强分支：增强目标边缘信息
- 自适应融合：学习最优融合权重

### 2. 多尺度特征金字塔网络
- 基于FCOS的改进FPN结构
- 自顶向下的特征融合
- 横向连接增强特征表达

### 3. 质量感知分类损失
- 结合分类置信度和定位质量
- 动态调整损失权重
- 提高小目标检测精度

### 4. 任务对齐学习
- 通道注意力和空间注意力
- 任务特定的特征对齐
- 分类和回归任务协同优化

## 🚨 注意事项

### 训练建议
- 首次训练建议使用较小的batch_size
- 监控GPU显存使用情况
- 定期保存检查点避免训练中断
- 使用验证集监控过拟合

### 推理建议
- 推理前确保模型已加载
- 大批量推理时注意内存使用
- 根据需要调整置信度阈值

### 故障排除
- 如果显存不足，减小batch_size
- 如果训练速度慢，检查CUDA安装
- 如果精度不高，尝试调整学习率

## 📈 性能监控

### 训练监控
- 实时显示训练和验证损失
- 自动保存最佳模型
- 生成训练曲线图

### 评估指标
- 检测精度 (mAP)
- 召回率 (Recall)
- 精确率 (Precision)
- F1分数

## 🔄 版本对比

| 特性 | v1.0 (传统版) | v2.0 (深度学习版) |
|------|---------------|-------------------|
| 框架 | OpenCV + NumPy | PyTorch |
| 训练 | 无需训练 | 端到端训练 |
| 精度 | 中等 | 高 |
| 速度 | 快 | 中等（GPU加速后快） |
| 显存需求 | 低 | 中等 |
| 扩展性 | 有限 | 强 |

## 📞 技术支持

如果您在使用过程中遇到问题，请：
1. 首先运行测试脚本检查环境
2. 查看错误日志和堆栈跟踪
3. 检查GPU和CUDA环境
4. 确认数据集格式正确

## 🎉 总结

红外小目标检测器v2.0是一个完整的深度学习解决方案，融合了当前最先进的目标检测算法。通过针对红外小目标的专门优化，能够在实际应用中取得良好的检测效果。

**主要优势：**
- 🚀 深度学习端到端训练
- 🎯 多种先进算法融合
- 💪 GPU加速高效训练
- 🔧 完整的工具链支持
