#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版训练脚本 - 针对4GB显存优化
"""

import os
import torch
import gc
from infrared_detector2 import (
    create_model, 
    create_data_loaders, 
    InfraredDetectorTrainer
)

def clear_gpu_memory():
    """清理GPU显存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()

def optimize_gpu_settings():
    """优化GPU设置"""
    if torch.cuda.is_available():
        # 设置显存分配策略
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
        
        # 启用显存碎片整理
        torch.cuda.memory._set_allocator_settings('expandable_segments:True')
        
        print(f"🖥️ GPU设备: {torch.cuda.get_device_name()}")
        print(f"🧠 总显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB")

def train_with_memory_optimization():
    """显存优化训练"""
    print("🚀 启动显存优化训练")
    print("=" * 50)
    
    # 优化GPU设置
    optimize_gpu_settings()
    
    # 清理显存
    clear_gpu_memory()
    
    # 设备选择
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"📱 使用设备: {device}")
    
    # 获取数据集路径
    dataset_path = input("📁 请输入数据集路径 (默认: D:\\WTdataset): ").strip()
    if not dataset_path:
        dataset_path = "D:\\WTdataset"
    
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return
    
    # 优化的训练配置
    config = {
        'epochs': 30,           # 减少训练轮数
        'batch_size': 1,        # 最小batch size
        'learning_rate': 2e-4,  # 稍微提高学习率补偿小batch size
        'weight_decay': 1e-4,
        'input_size': 320,      # 进一步减小输入尺寸
        'num_classes': 1
    }
    
    print(f"📊 优化训练配置:")
    print(f"  - Epochs: {config['epochs']}")
    print(f"  - Batch Size: {config['batch_size']}")
    print(f"  - Input Size: {config['input_size']}")
    print(f"  - Learning Rate: {config['learning_rate']}")
    
    try:
        # 创建轻量化模型
        print("\n🔧 创建轻量化模型...")
        model = create_model(config['num_classes'], config['input_size'])
        
        # 显示模型参数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"📊 模型参数数: {total_params:,}")
        
        # 创建数据加载器
        print("📂 加载数据...")
        train_loader, val_loader = create_data_loaders(
            dataset_path, 
            batch_size=config['batch_size'],
            num_workers=0  # 设为0避免多进程内存问题
        )
        
        print(f"📈 数据集大小:")
        print(f"  - 训练集: {len(train_loader.dataset)}")
        print(f"  - 验证集: {len(val_loader.dataset)}")
        
        # 清理显存
        clear_gpu_memory()
        
        # 创建训练器
        print("🎯 初始化训练器...")
        trainer = InfraredDetectorTrainer(model, train_loader, val_loader, device, config)
        
        # 显示显存使用情况
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated() / 1024**3
            reserved = torch.cuda.memory_reserved() / 1024**3
            print(f"💾 显存使用: {allocated:.2f}GB 已分配, {reserved:.2f}GB 已保留")
        
        # 开始训练
        print("\n🚀 开始训练...")
        trainer.train()
        
    except torch.cuda.OutOfMemoryError as e:
        print(f"❌ 显存不足: {e}")
        print("\n💡 建议:")
        print("  1. 进一步减小batch_size到1")
        print("  2. 减小input_size到256")
        print("  3. 关闭其他占用显存的程序")
        print("  4. 重启Python进程清理显存")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理显存
        clear_gpu_memory()

def main():
    """主函数"""
    print("🎯 红外小目标检测器 - 显存优化训练")
    print("=" * 50)
    print("🔧 针对4GB显存优化的训练脚本")
    print("✨ 自动优化显存使用和模型参数")
    print()
    
    # 检查环境
    if not torch.cuda.is_available():
        print("⚠️ 未检测到CUDA，将使用CPU训练（速度较慢）")
    
    # 开始训练
    train_with_memory_optimization()
    
    print("\n🎉 训练脚本执行完成!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出!")
        clear_gpu_memory()
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        clear_gpu_memory()
        import traceback
        traceback.print_exc()
