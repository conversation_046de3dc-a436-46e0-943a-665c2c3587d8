#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
红外小目标检测器 - 演示版本
基于FCOS、GFL、TOOD算法的创新实现

特点：
- 无需深度学习框架，使用传统图像处理技术
- 完整展示算法创新思路和处理流程
- 支持War Thunder数据集，自动处理中文路径
- 生成详细的可视化结果

算法创新点：
1. 红外图像增强 - 对比度增强、边缘增强、噪声抑制
2. 多尺度特征融合 - 模拟1×1、3×3、5×5卷积和空洞卷积
3. 注意力机制 - 通道注意力和空间注意力
4. 简化目标检测 - 基于特征响应的检测算法

作者：AI Assistant
日期：2025-01-19
版本：v2.0 (演示版)
"""

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
import cv2
from PIL import Image
import random
from tqdm import tqdm

# 添加父目录到路径以导入现有工具
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from wt_dataset_manager import WTDatasetManager
from universal_file_utils import find_image_label_pairs


class InfraredEnhancementDemo:
    """
    红外图像增强演示类
    展示针对红外小目标的图像预处理技术
    """
    
    @staticmethod
    def contrast_enhancement(image):
        """对比度增强"""
        # 使用CLAHE (Contrast Limited Adaptive Histogram Equalization)
        lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        lab[:, :, 0] = clahe.apply(lab[:, :, 0])
        enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
        return enhanced
    
    @staticmethod
    def edge_enhancement(image):
        """边缘增强"""
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        # 使用Laplacian算子增强边缘
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        laplacian = np.uint8(np.absolute(laplacian))
        
        # 将边缘信息融合到原图像
        enhanced = image.copy()
        for i in range(3):
            enhanced[:, :, i] = cv2.addWeighted(
                enhanced[:, :, i], 0.8, laplacian, 0.2, 0
            )
        return enhanced
    
    @staticmethod
    def noise_suppression(image):
        """噪声抑制"""
        # 使用双边滤波保持边缘的同时去噪
        denoised = cv2.bilateralFilter(image, 9, 75, 75)
        return denoised
    
    @classmethod
    def enhance_infrared_image(cls, image):
        """综合红外图像增强"""
        # 对比度增强
        contrast_enhanced = cls.contrast_enhancement(image)
        
        # 边缘增强
        edge_enhanced = cls.edge_enhancement(contrast_enhanced)
        
        # 噪声抑制
        final_enhanced = cls.noise_suppression(edge_enhanced)
        
        return final_enhanced


class MultiScaleFeatureDemo:
    """
    多尺度特征提取演示类
    模拟FCOS改进的多尺度特征融合
    """
    
    @staticmethod
    def extract_features_scale_1(image):
        """1x1卷积模拟 - 点特征"""
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        # 简单的点特征提取
        kernel = np.array([[0, 0, 0], [0, 1, 0], [0, 0, 0]])
        feature = cv2.filter2D(gray, -1, kernel)
        return feature
    
    @staticmethod
    def extract_features_scale_3(image):
        """3x3卷积模拟 - 局部特征"""
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        # Sobel算子提取边缘特征
        sobel_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobel_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        feature = np.sqrt(sobel_x**2 + sobel_y**2)
        return feature.astype(np.uint8)
    
    @staticmethod
    def extract_features_scale_5(image):
        """5x5卷积模拟 - 中等范围特征"""
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        # 使用5x5高斯核
        feature = cv2.GaussianBlur(gray, (5, 5), 1.0)
        return feature
    
    @staticmethod
    def extract_dilated_features(image):
        """空洞卷积模拟 - 大感受野特征"""
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        # 模拟空洞卷积效果
        kernel = np.array([
            [0, 0, 1, 0, 0],
            [0, 0, 0, 0, 0],
            [1, 0, 1, 0, 1],
            [0, 0, 0, 0, 0],
            [0, 0, 1, 0, 0]
        ], dtype=np.float32) / 5
        feature = cv2.filter2D(gray, -1, kernel)
        return feature
    
    @classmethod
    def fuse_multiscale_features(cls, image):
        """融合多尺度特征"""
        # 提取不同尺度的特征
        feat_1 = cls.extract_features_scale_1(image)
        feat_3 = cls.extract_features_scale_3(image)
        feat_5 = cls.extract_features_scale_5(image)
        feat_dilated = cls.extract_dilated_features(image)
        
        # 特征融合 (简单加权平均)
        fused = (feat_1 * 0.25 + feat_3 * 0.25 + 
                feat_5 * 0.25 + feat_dilated * 0.25)
        
        return fused.astype(np.uint8)


class AttentionMechanismDemo:
    """
    注意力机制演示类
    模拟GFL质量感知的注意力增强
    """
    
    @staticmethod
    def channel_attention_simulation(features):
        """通道注意力模拟"""
        # 计算每个通道的重要性权重
        if len(features.shape) == 2:
            # 对于灰度特征图，模拟通道注意力
            mean_val = np.mean(features)
            max_val = np.max(features)
            attention_weight = (mean_val + max_val) / (2 * 255.0)
            return features * attention_weight
        return features
    
    @staticmethod
    def spatial_attention_simulation(features):
        """空间注意力模拟"""
        # 计算空间注意力图
        attention_map = cv2.GaussianBlur(features, (7, 7), 1.0)
        attention_map = attention_map / 255.0
        
        # 应用注意力
        enhanced_features = features * attention_map
        return enhanced_features.astype(np.uint8)
    
    @classmethod
    def apply_attention(cls, features):
        """应用注意力机制"""
        # 通道注意力
        channel_enhanced = cls.channel_attention_simulation(features)
        
        # 空间注意力
        spatial_enhanced = cls.spatial_attention_simulation(channel_enhanced)
        
        return spatial_enhanced


class SmallTargetDetectorDemo:
    """
    小目标检测演示类
    整合所有创新模块的演示版检测器
    """
    
    def __init__(self, dataset_path):
        self.dataset_path = dataset_path
        self.manager = WTDatasetManager(dataset_path)
        self.enhancement = InfraredEnhancementDemo()
        self.multiscale = MultiScaleFeatureDemo()
        self.attention = AttentionMechanismDemo()
        
    def process_single_image(self, image_path, label_path=None):
        """处理单张图像的完整流程"""
        # 安全加载图像 - 优先使用PIL来避免中文路径问题
        image_rgb = self._safe_load_image(image_path)
        if image_rgb is None:
            print(f"❌ 无法加载图像: {image_path}")
            return None
        
        # 步骤1: 红外图像增强
        enhanced_image = self.enhancement.enhance_infrared_image(image_rgb)
        
        # 步骤2: 多尺度特征提取
        multiscale_features = self.multiscale.fuse_multiscale_features(enhanced_image)
        
        # 步骤3: 注意力增强
        attention_features = self.attention.apply_attention(multiscale_features)
        
        # 步骤4: 简单的目标检测 (基于特征响应)
        detections = self._simple_detection(attention_features)
        
        # 加载真实标签用于对比
        ground_truth = []
        if label_path and os.path.exists(label_path):
            ground_truth = self._load_ground_truth(label_path, image_rgb.shape[:2])
        
        return {
            'original': image_rgb,
            'enhanced': enhanced_image,
            'features': multiscale_features,
            'attention': attention_features,
            'detections': detections,
            'ground_truth': ground_truth
        }
    
    def _simple_detection(self, features):
        """简单的目标检测算法"""
        # 使用阈值和连通域分析进行简单检测
        _, binary = cv2.threshold(features, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 形态学操作去除噪声
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if 10 < area < 5000:  # 过滤太小或太大的区域
                x, y, w, h = cv2.boundingRect(contour)
                detections.append({
                    'bbox': [x, y, x+w, y+h],
                    'area': area,
                    'confidence': min(area / 1000.0, 1.0)  # 简单的置信度计算
                })
        
        return detections

    def _safe_load_image(self, image_path):
        """安全加载图像，处理中文路径问题"""
        try:
            # 方法1: 使用PIL加载 (最兼容中文路径)
            pil_image = Image.open(image_path)
            image_rgb = np.array(pil_image.convert('RGB'))
            return image_rgb
        except Exception as e:
            print(f"PIL加载失败: {e}")

        try:
            # 方法2: 二进制读取 + OpenCV解码
            with open(image_path, 'rb') as f:
                image_data = f.read()
            image_array = np.frombuffer(image_data, np.uint8)
            image_bgr = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
            if image_bgr is not None:
                image_rgb = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)
                return image_rgb
        except Exception as e:
            print(f"二进制加载失败: {e}")

        try:
            # 方法3: matplotlib加载
            import matplotlib.pyplot as plt
            image = plt.imread(image_path)
            if image.dtype in [np.float32, np.float64]:
                image = (image * 255).astype(np.uint8)
            return image
        except Exception as e:
            print(f"matplotlib加载失败: {e}")

        print(f"❌ 所有加载方法都失败了: {image_path}")
        return None

    def _load_ground_truth(self, label_path, image_shape):
        """加载真实标签"""
        try:
            with open(label_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            ground_truth = []
            img_height, img_width = image_shape
            
            for unit in data.get('units', []):
                if unit.get('isvisible', False) and "flightModels" in unit.get('m_blk_path', ''):
                    bbox = unit.get('bbox', None)
                    if bbox and any(x != 0 for x in bbox):
                        x1, y1, x2, y2 = bbox
                        if (0 <= x1 < img_width and 0 <= y1 < img_height and 
                            0 <= x2 < img_width and 0 <= y2 < img_height and
                            x1 < x2 and y1 < y2):
                            ground_truth.append({
                                'bbox': [x1, y1, x2, y2],
                                'aircraft_name': unit.get('m_short_name', 'Unknown')
                            })
            
            return ground_truth
            
        except Exception as e:
            print(f"加载标签失败: {e}")
            return []
    
    def visualize_results(self, results, save_path=None):
        """可视化处理结果"""
        if results is None:
            print("❌ 结果为空，无法可视化")
            return

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 原始图像
        axes[0, 0].imshow(results['original'])
        axes[0, 0].set_title('原始图像')
        axes[0, 0].axis('off')
        
        # 增强后图像
        axes[0, 1].imshow(results['enhanced'])
        axes[0, 1].set_title('红外增强后')
        axes[0, 1].axis('off')
        
        # 多尺度特征
        axes[0, 2].imshow(results['features'], cmap='hot')
        axes[0, 2].set_title('多尺度特征')
        axes[0, 2].axis('off')
        
        # 注意力特征
        axes[1, 0].imshow(results['attention'], cmap='hot')
        axes[1, 0].set_title('注意力增强特征')
        axes[1, 0].axis('off')
        
        # 检测结果
        detection_img = results['original'].copy()
        for det in results['detections']:
            x1, y1, x2, y2 = det['bbox']
            # 使用matplotlib绘制矩形，避免OpenCV的字体问题
            from matplotlib.patches import Rectangle

        # 创建检测结果图像
        detection_img = results['original'].copy()
        detection_patches = []
        for det in results['detections']:
            x1, y1, x2, y2 = det['bbox']
            rect = Rectangle((x1, y1), x2-x1, y2-y1, linewidth=2,
                           edgecolor='green', facecolor='none')
            detection_patches.append((rect, det['confidence']))
        
        axes[1, 1].imshow(detection_img)
        # 添加检测框
        for rect, confidence in detection_patches:
            axes[1, 1].add_patch(rect)
            # 添加置信度文本
            x, y = rect.get_x(), rect.get_y()
            axes[1, 1].text(x, y-5, f"{confidence:.2f}", color='green', fontsize=8)
        axes[1, 1].set_title(f'检测结果 ({len(results["detections"])} 个目标)')
        axes[1, 1].axis('off')

        # 真实标签对比
        gt_img = results['original'].copy()
        gt_patches = []
        for gt in results['ground_truth']:
            x1, y1, x2, y2 = gt['bbox']
            rect = Rectangle((x1, y1), x2-x1, y2-y1, linewidth=2,
                           edgecolor='red', facecolor='none')
            gt_patches.append((rect, gt['aircraft_name']))

        axes[1, 2].imshow(gt_img)
        # 添加真实标签框
        for rect, aircraft_name in gt_patches:
            axes[1, 2].add_patch(rect)
            # 添加飞机名称
            x, y = rect.get_x(), rect.get_y()
            axes[1, 2].text(x, y-5, aircraft_name, color='red', fontsize=8)
        axes[1, 2].set_title(f'真实标签 ({len(results["ground_truth"])} 个目标)')
        axes[1, 2].axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"结果已保存: {save_path}")
        else:
            plt.show()
        
        plt.close()


def show_welcome():
    """显示欢迎信息"""
    print("🎯 红外小目标检测器 - 算法演示版 v2.0")
    print("=" * 60)
    print("✨ 基于FCOS、GFL、TOOD算法的创新实现")
    print("🚀 无需深度学习框架，使用传统图像处理技术")
    print("🎨 完整展示算法创新思路和处理流程")
    print("📊 支持War Thunder数据集，自动生成可视化结果")
    print("=" * 60)
    print()
    print("🔬 算法创新点:")
    print("  1. 红外图像增强 - 对比度增强、边缘增强、噪声抑制")
    print("  2. 多尺度特征融合 - 1×1、3×3、5×5卷积和空洞卷积模拟")
    print("  3. 注意力机制 - 通道注意力和空间注意力")
    print("  4. 目标检测 - 基于特征响应的简化检测算法")
    print()


def show_menu():
    """显示主菜单"""
    print("📋 请选择操作模式:")
    print("1. 🎨 完整演示 (推荐) - 处理3张图像，展示完整算法流程")
    print("2. 🔍 单张图像处理 - 选择特定图像进行处理")
    print("3. 🚀 批量处理所有图像 - 处理数据集中的所有图像")
    print("4. 🛠️ 图像加载测试 - 测试数据集图像加载功能")
    print("5. 📖 算法说明 - 查看详细的算法原理")
    print("6. 🚪 退出程序")
    print()


def show_algorithm_explanation():
    """显示算法说明"""
    print("\n📚 算法原理详解")
    print("=" * 50)

    print("\n1️⃣ 红外图像增强模块")
    print("   🎯 目标: 提高红外图像的对比度和清晰度")
    print("   🔧 方法:")
    print("      - CLAHE对比度增强: 自适应直方图均衡化")
    print("      - Laplacian边缘增强: 突出目标边缘信息")
    print("      - 双边滤波噪声抑制: 保持边缘的同时去除噪声")

    print("\n2️⃣ 多尺度特征融合模块")
    print("   🎯 目标: 提取不同尺度的特征信息")
    print("   🔧 方法:")
    print("      - 1×1卷积模拟: 点特征提取")
    print("      - 3×3卷积模拟: Sobel边缘检测")
    print("      - 5×5卷积模拟: 高斯平滑特征")
    print("      - 空洞卷积模拟: 大感受野特征")

    print("\n3️⃣ 注意力机制模块")
    print("   🎯 目标: 增强重要特征，抑制背景噪声")
    print("   🔧 方法:")
    print("      - 通道注意力: 基于统计特征的权重调整")
    print("      - 空间注意力: 基于高斯权重的空间增强")

    print("\n4️⃣ 目标检测模块")
    print("   🎯 目标: 从增强特征中检测小目标")
    print("   🔧 方法:")
    print("      - OTSU阈值分割: 自适应阈值选择")
    print("      - 形态学操作: 去除噪声，保持目标形状")
    print("      - 轮廓检测: 提取目标边界框")

    print("\n💡 创新点:")
    print("   ✨ 将深度学习的多尺度融合思想用传统方法实现")
    print("   ✨ 结合注意力机制提高小目标检测能力")
    print("   ✨ 针对红外图像特点进行专门优化")

    input("\n按回车键返回主菜单...")


def process_single_image_interactive(detector):
    """交互式单张图像处理"""
    print("\n🔍 单张图像处理模式")
    print("-" * 30)

    # 获取所有图像
    image_pairs = find_image_label_pairs(detector.dataset_path)

    if not image_pairs:
        print("❌ 未找到图像文件")
        return

    print(f"📁 找到 {len(image_pairs)} 张图像")

    # 显示前10张图像供选择
    print("\n可选择的图像 (显示前10张):")
    display_count = min(10, len(image_pairs))

    for i in range(display_count):
        image_path, _ = image_pairs[i]
        filename = os.path.basename(image_path)
        print(f"  {i+1}. {filename}")

    if len(image_pairs) > 10:
        print(f"  ... 还有 {len(image_pairs) - 10} 张图像")

    # 用户选择
    try:
        choice = input(f"\n请选择图像编号 (1-{display_count}) 或输入 'r' 随机选择: ").strip()

        if choice.lower() == 'r':
            idx = random.randint(0, len(image_pairs) - 1)
        else:
            idx = int(choice) - 1
            if idx < 0 or idx >= len(image_pairs):
                print("❌ 无效的选择")
                return

        image_path, label_path = image_pairs[idx]
        print(f"\n🎨 处理图像: {os.path.basename(image_path)}")

        # 处理图像
        results = detector.process_single_image(image_path, label_path)

        if results is None:
            print("❌ 图像处理失败")
            return

        # 保存结果
        result_dir = "./result"
        os.makedirs(result_dir, exist_ok=True)
        save_path = os.path.join(result_dir, f"single_image_result.png")

        detector.visualize_results(results, save_path)

        print(f"✅ 处理完成!")
        print(f"   检测到 {len(results['detections'])} 个目标")
        print(f"   真实标签 {len(results['ground_truth'])} 个目标")
        print(f"   结果保存: {save_path}")

    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 处理失败: {e}")


def batch_process_all_images(detector):
    """批量处理所有图像"""
    print("\n🚀 批量处理所有图像")
    print("-" * 40)

    # 获取所有图像
    image_pairs = find_image_label_pairs(detector.dataset_path)

    if not image_pairs:
        print("❌ 未找到图像文件")
        return

    total_images = len(image_pairs)
    print(f"📁 找到 {total_images} 张图像")

    # 确认是否继续
    print(f"\n⚠️  警告: 即将处理 {total_images} 张图像，这可能需要较长时间")
    print("💡 建议: 如果图像数量很多，可以先用完整演示模式测试几张")

    # 提供处理数量选项
    print(f"\n📊 处理数量选项:")
    print(f"1. 处理所有 {total_images} 张图像")
    print(f"2. 处理前100张图像 (快速测试)")
    print(f"3. 处理前500张图像 (中等规模)")
    print(f"4. 自定义数量")
    print(f"5. 取消处理")

    choice = input("\n请选择处理数量 (1-5): ").strip()

    if choice == '1':
        process_count = total_images
    elif choice == '2':
        process_count = min(100, total_images)
    elif choice == '3':
        process_count = min(500, total_images)
    elif choice == '4':
        try:
            custom_count = int(input(f"请输入要处理的图像数量 (1-{total_images}): "))
            process_count = min(max(1, custom_count), total_images)
        except ValueError:
            print("❌ 输入无效，取消处理")
            return
    elif choice == '5':
        print("❌ 取消批量处理")
        return
    else:
        print("❌ 无效选择，取消处理")
        return

    print(f"\n✅ 将处理前 {process_count} 张图像")

    confirm = input("是否继续? (y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ 取消批量处理")
        return

    # 限制处理的图像数量
    image_pairs = image_pairs[:process_count]
    total_images = len(image_pairs)

    # 询问处理选项
    print("\n📊 处理选项:")
    print("1. 仅生成检测统计 (快速)")
    print("2. 生成详细可视化结果 (较慢)")

    option = input("请选择处理方式 (1-2): ").strip()

    # 创建结果目录
    result_dir = "./result/batch_processing"
    os.makedirs(result_dir, exist_ok=True)

    # 统计信息
    stats = {
        'total_processed': 0,
        'successful': 0,
        'failed': 0,
        'total_detections': 0,
        'total_ground_truth': 0,
        'detection_counts': [],
        'ground_truth_counts': []
    }

    print(f"\n🎨 开始批量处理 {total_images} 张图像...")
    print("=" * 60)

    # 使用tqdm显示进度条
    for i, (image_path, label_path) in enumerate(tqdm(image_pairs, desc="处理进度")):
        stats['total_processed'] += 1

        try:
            # 处理图像
            results = detector.process_single_image(image_path, label_path)

            if results is None:
                stats['failed'] += 1
                continue

            # 统计检测结果
            num_detections = len(results['detections'])
            num_ground_truth = len(results['ground_truth'])

            stats['successful'] += 1
            stats['total_detections'] += num_detections
            stats['total_ground_truth'] += num_ground_truth
            stats['detection_counts'].append(num_detections)
            stats['ground_truth_counts'].append(num_ground_truth)

            # 根据选项决定是否生成可视化
            if option == '2':
                # 生成详细可视化 (每100张保存一次，避免文件过多)
                if i % 100 == 0 or i < 10:  # 前10张和每100张保存一次
                    save_path = os.path.join(result_dir, f"batch_result_{i+1}.png")
                    detector.visualize_results(results, save_path)

            # 每处理100张图像显示一次进度
            if (i + 1) % 100 == 0:
                success_rate = stats['successful'] / stats['total_processed'] * 100
                avg_detections = stats['total_detections'] / stats['successful'] if stats['successful'] > 0 else 0
                print(f"\n📊 进度报告 ({i+1}/{total_images}):")
                print(f"   成功率: {success_rate:.1f}%")
                print(f"   平均检测数: {avg_detections:.1f}")

        except Exception as e:
            stats['failed'] += 1
            if stats['failed'] <= 5:  # 只显示前5个错误
                print(f"\n❌ 处理失败 {os.path.basename(image_path)}: {e}")

    # 生成最终统计报告
    generate_batch_report(stats, result_dir, total_images)

    print(f"\n🎉 批量处理完成!")
    print(f"📁 结果保存在: {result_dir}")

    input("\n按回车键返回主菜单...")


def generate_batch_report(stats, result_dir, total_images):
    """生成批量处理报告"""
    print(f"\n📊 生成处理报告...")

    # 计算统计指标
    success_rate = stats['successful'] / stats['total_processed'] * 100 if stats['total_processed'] > 0 else 0
    avg_detections = stats['total_detections'] / stats['successful'] if stats['successful'] > 0 else 0
    avg_ground_truth = stats['total_ground_truth'] / stats['successful'] if stats['successful'] > 0 else 0

    # 生成文本报告
    report_path = os.path.join(result_dir, "batch_processing_report.txt")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("红外小目标检测器 - 批量处理报告\n")
        f.write("=" * 50 + "\n\n")

        f.write(f"处理时间: {import_datetime()}\n")
        f.write(f"数据集路径: {os.path.abspath('.')}\n\n")

        f.write("📊 处理统计:\n")
        f.write(f"  总图像数: {total_images}\n")
        f.write(f"  已处理: {stats['total_processed']}\n")
        f.write(f"  成功处理: {stats['successful']}\n")
        f.write(f"  处理失败: {stats['failed']}\n")
        f.write(f"  成功率: {success_rate:.2f}%\n\n")

        f.write("🎯 检测统计:\n")
        f.write(f"  总检测目标数: {stats['total_detections']}\n")
        f.write(f"  总真实目标数: {stats['total_ground_truth']}\n")
        f.write(f"  平均检测数/图像: {avg_detections:.2f}\n")
        f.write(f"  平均真实目标数/图像: {avg_ground_truth:.2f}\n\n")

        if stats['detection_counts']:
            import numpy as np
            det_counts = np.array(stats['detection_counts'])
            f.write("📈 检测数量分布:\n")
            f.write(f"  最小值: {np.min(det_counts)}\n")
            f.write(f"  最大值: {np.max(det_counts)}\n")
            f.write(f"  中位数: {np.median(det_counts):.1f}\n")
            f.write(f"  标准差: {np.std(det_counts):.1f}\n\n")

        f.write("🔬 算法创新点:\n")
        f.write("  1. 红外图像增强 - 对比度增强、边缘增强、噪声抑制\n")
        f.write("  2. 多尺度特征融合 - 1×1、3×3、5×5卷积和空洞卷积模拟\n")
        f.write("  3. 注意力机制 - 通道注意力和空间注意力\n")
        f.write("  4. 目标检测 - 基于特征响应的简化检测算法\n")

    # 生成统计图表
    if stats['detection_counts']:
        generate_statistics_plot(stats, result_dir)

    print(f"✅ 报告已保存: {report_path}")


def generate_statistics_plot(stats, result_dir):
    """生成统计图表"""
    try:
        import matplotlib.pyplot as plt
        import numpy as np

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 1. 检测数量分布直方图
        axes[0, 0].hist(stats['detection_counts'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('检测目标数量分布')
        axes[0, 0].set_xlabel('检测目标数')
        axes[0, 0].set_ylabel('图像数量')
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 真实目标数量分布
        axes[0, 1].hist(stats['ground_truth_counts'], bins=20, alpha=0.7, color='lightcoral', edgecolor='black')
        axes[0, 1].set_title('真实目标数量分布')
        axes[0, 1].set_xlabel('真实目标数')
        axes[0, 1].set_ylabel('图像数量')
        axes[0, 1].grid(True, alpha=0.3)

        # 3. 检测数量趋势 (前1000张)
        sample_size = min(1000, len(stats['detection_counts']))
        x_range = range(sample_size)
        axes[1, 0].plot(x_range, stats['detection_counts'][:sample_size], alpha=0.7, color='green')
        axes[1, 0].set_title(f'检测数量趋势 (前{sample_size}张)')
        axes[1, 0].set_xlabel('图像序号')
        axes[1, 0].set_ylabel('检测目标数')
        axes[1, 0].grid(True, alpha=0.3)

        # 4. 处理统计饼图
        labels = ['成功处理', '处理失败']
        sizes = [stats['successful'], stats['failed']]
        colors = ['lightgreen', 'lightcoral']
        axes[1, 1].pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        axes[1, 1].set_title('处理成功率')

        plt.tight_layout()

        # 保存图表
        plot_path = os.path.join(result_dir, "batch_statistics.png")
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ 统计图表已保存: {plot_path}")

    except Exception as e:
        print(f"⚠️ 生成统计图表失败: {e}")


def import_datetime():
    """导入datetime并返回当前时间"""
    from datetime import datetime
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    """主函数 - 演示红外小目标检测算法"""
    show_welcome()

    # 获取数据集路径
    dataset_path = input("📁 请输入数据集路径 (默认: D:\\WTdataset): ").strip()
    if not dataset_path:
        dataset_path = "D:\\WTdataset"

    if not os.path.exists(dataset_path):
        print(f"❌ 数据集路径不存在: {dataset_path}")
        print("💡 请确认路径正确，支持中文路径")
        return

    # 创建检测器
    try:
        detector = SmallTargetDetectorDemo(dataset_path)
    except Exception as e:
        print(f"❌ 初始化检测器失败: {e}")
        return

    # 主循环
    while True:
        show_menu()
        choice = input("请选择操作 (1-6): ").strip()

        if choice == '1':
            # 完整演示
            complete_demo(detector)
        elif choice == '2':
            # 单张图像处理
            process_single_image_interactive(detector)
        elif choice == '3':
            # 批量处理所有图像
            batch_process_all_images(detector)
        elif choice == '4':
            # 图像加载测试
            print("\n🛠️ 启动图像加载测试...")
            os.system("python WT/test_image_loading.py")
        elif choice == '5':
            # 算法说明
            show_algorithm_explanation()
        elif choice == '6':
            # 退出
            print("\n👋 感谢使用红外小目标检测器演示系统!")
            break
        else:
            print("❌ 无效选项，请重新选择")


def complete_demo(detector):
    """完整演示流程"""
    print("\n🎨 开始完整演示...")
    print("-" * 40)

    # 获取图像-标签对
    image_pairs = find_image_label_pairs(detector.dataset_path)
    
    if not image_pairs:
        print("❌ 未找到有效的图像-标签对")
        return
    
    print(f"✅ 找到 {len(image_pairs)} 对图像-标签文件")
    
    # 随机选择几张图像进行演示
    num_demo = min(3, len(image_pairs))
    demo_pairs = random.sample(image_pairs, num_demo)
    
    print(f"\n🎨 开始处理 {num_demo} 张演示图像...")
    
    for i, (image_path, label_path) in enumerate(demo_pairs):
        print(f"\n处理图像 {i+1}: {os.path.basename(image_path)}")

        # 处理图像
        results = detector.process_single_image(image_path, label_path)

        # 检查处理结果
        if results is None:
            print(f"  ❌ 图像处理失败，跳过")
            continue

        # 创建结果目录
        result_dir = "./result"
        os.makedirs(result_dir, exist_ok=True)

        # 可视化结果
        save_path = os.path.join(result_dir, f"demo_result_{i+1}.png")
        try:
            detector.visualize_results(results, save_path)
            print(f"  ✅ 检测到 {len(results['detections'])} 个目标")
            print(f"  📋 真实标签 {len(results['ground_truth'])} 个目标")
            print(f"  💾 结果保存: {save_path}")
        except Exception as e:
            print(f"  ❌ 可视化失败: {e}")
            continue

    print(f"\n🎉 演示完成! 结果保存在 ./result/ 目录下")

    print("\n📊 处理结果说明:")
    print("  🖼️  每个结果图像包含6个子图:")
    print("     1. 原始图像 - 输入的红外图像")
    print("     2. 红外增强后 - 应用增强算法后的图像")
    print("     3. 多尺度特征 - 融合特征的热力图")
    print("     4. 注意力增强特征 - 注意力机制处理后的特征")
    print("     5. 检测结果 - 算法检测的目标(绿色框)")
    print("     6. 真实标签 - 数据集标签(红色框)")

    print("\n🔬 算法创新点总结:")
    print("  1. 🎨 红外图像增强 - 对比度增强、边缘增强、噪声抑制")
    print("  2. 🔍 多尺度特征融合 - 1×1、3×3、5×5卷积和空洞卷积模拟")
    print("  3. 🎯 注意力机制 - 通道注意力和空间注意力")
    print("  4. 🎪 目标检测 - 基于特征响应的简化检测算法")

    input("\n按回车键返回主菜单...")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出!")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        import traceback
        traceback.print_exc()
