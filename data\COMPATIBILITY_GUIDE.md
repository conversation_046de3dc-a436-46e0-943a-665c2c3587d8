# War Thunder 数据集工具兼容性指南

## 🎯 重要说明

**好消息：所有工具已经完全兼容重命名前后的文件格式！** 学生可以安全地进行一键重命名，不会影响后续的任何功能。

## 📋 兼容性测试结果

### ✅ 测试通过项目
- **文件格式自动检测** ✅
- **原始格式支持** ✅ (output_images*.png, video_label*.json)
- **重命名格式支持** ✅ (batch*_*_*.png, batch*_*_*.json)
- **混合格式处理** ✅
- **所有工具模块导入** ✅
- **核心功能验证** ✅

### 🛠️ 兼容的工具列表
1. **wt_dataset_manager.py** - 主要管理工具
2. **visualize_bbox_fixed.py** - 修复版可视化工具
3. **debug_bbox.py** - BBox调试工具
4. **universal_file_utils.py** - 通用文件工具
5. **所有相关脚本** - 启动脚本、演示脚本等

## 🔄 文件格式支持

### 原始格式（重命名前）
```
output_images0.png    ←→  video_label0.json
output_images1.png    ←→  video_label1.json
output_images123.png  ←→  video_label123.json
...
```

### 重命名格式（重命名后）
```
batch1_20230001_1.png   ←→  batch1_20230001_1.json
batch1_20230001_2.png   ←→  batch1_20230001_2.json
batch2_20230002_1.png   ←→  batch2_20230002_1.json
...
```

### 通用格式（兜底方案）
```
任何_文件名.png  ←→  任何_文件名.json
```

## 🚀 使用流程

### 步骤1：重命名前（可选）
```bash
# 数据集质量评估
python wt_dataset_manager.py --task validate

# BBox可视化（原始格式）
python visualize_bbox_fixed.py
```

### 步骤2：执行重命名
```bash
# 一键重命名
python wt_dataset_manager.py --task rename --batch_id 1 --student_id 20230001
```

### 步骤3：重命名后
```bash
# 所有功能照常可用！
python wt_dataset_manager.py --task filter    # 数据过滤
python wt_dataset_manager.py --task visualize # 可视化
python visualize_bbox_fixed.py                # 修复版可视化
python debug_bbox.py                          # 调试工具
```

## 🔧 技术实现

### 自动格式检测
```python
def detect_file_format(dataset_path):
    """自动检测文件格式"""
    original_count = len(glob.glob(os.path.join(dataset_path, "output_images*.png")))
    renamed_count = len(glob.glob(os.path.join(dataset_path, "batch*_*_*.png")))
    
    if original_count > 0 and renamed_count == 0:
        return 'original'
    elif original_count == 0 and renamed_count > 0:
        return 'renamed'
    elif original_count > 0 and renamed_count > 0:
        return 'mixed'
    else:
        return 'unknown'
```

### 通用文件配对
```python
def find_image_label_pairs(dataset_path):
    """查找所有格式的图像标签配对"""
    pairs = []
    
    # 方法1: 原始格式
    original_images = glob.glob(os.path.join(dataset_path, "output_images*.png"))
    for image_file in original_images:
        # 处理 output_images123.png -> video_label123.json
        
    # 方法2: 重命名格式
    renamed_images = glob.glob(os.path.join(dataset_path, "batch*_*_*.png"))
    for image_file in renamed_images:
        # 处理 batch1_20230001_1.png -> batch1_20230001_1.json
        
    # 方法3: 通用格式（兜底）
    if not pairs:
        all_images = glob.glob(os.path.join(dataset_path, "*.png"))
        for image_file in all_images:
            # 通用 *.png -> *.json
```

## 📊 性能特点

- **零配置**：工具自动检测文件格式
- **向后兼容**：原始格式完全支持
- **向前兼容**：重命名格式完全支持
- **容错处理**：混合格式和异常情况处理
- **性能优化**：优先匹配，降级处理

## ⚠️ 注意事项

### 1. 备份建议
虽然工具兼容性很好，但重命名前建议备份数据：
```bash
cp -r /path/to/dataset /path/to/dataset_backup
```

### 2. 文件完整性
重命名过程中确保：
- 图像和标签文件成对存在
- 没有中途中断操作
- 磁盘空间充足

### 3. 混合格式处理
如果数据集中同时存在两种格式：
- 工具会处理所有找到的有效配对
- 建议统一为一种格式
- 可以使用工具分别处理

## 🎉 总结

**学生可以放心使用一键重命名功能！**

✅ **完全兼容** - 重命名前后所有功能正常  
✅ **自动检测** - 无需手动配置文件格式  
✅ **容错处理** - 异常情况自动降级处理  
✅ **性能优化** - 快速准确的文件配对  

所有的数据集管理、过滤、可视化、调试功能在重命名后都能正常工作，学生无需担心兼容性问题。 