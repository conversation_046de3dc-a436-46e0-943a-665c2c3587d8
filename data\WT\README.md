# 红外小目标检测器 - 演示版本

## 🎯 项目简介

本项目是基于FCOS、GFL、TOOD等先进算法的红外小目标检测演示系统。使用传统图像处理技术模拟深度学习算法的创新思路，**无需安装深度学习框架**，可直接运行体验算法效果。

## 🚀 核心特色

### ✅ 无深度学习依赖
- 使用OpenCV、PIL等传统图像处理库
- 不需要安装PyTorch、TensorFlow等深度学习框架
- 轻量级，易于部署和使用

### 🎨 完整的处理流程
- **红外图像增强**: 对比度增强、边缘增强、噪声抑制
- **多尺度特征融合**: 模拟1×1、3×3、5×5卷积和空洞卷积
- **注意力机制**: 通道注意力和空间注意力模拟
- **目标检测**: 基于特征响应的简单检测算法

### 📊 可视化展示
- 完整的处理步骤可视化
- 检测结果与真实标签对比
- 算法效果直观展示

## 📁 文件结构

```
WT/
├── infrared_detector.py    # 主演示程序
├── README.md                   # 本文档


result/
```

## 🛠️ 环境要求

### 必需依赖
```bash
pip install numpy matplotlib opencv-python pillow tqdm
```

### 系统要求
- **Python**: 3.7+
- **操作系统**: Windows/Linux/macOS
- **内存**: 4GB以上
- **存储**: 1GB可用空间

## 📖 使用方法

### 1. 快速开始
```bash
cd WT
python infrared_detector_demo.py
```

### 2. 输入数据集路径
程序会提示输入数据集路径，支持：
- War Thunder数据集格式
- 包含图像和JSON标签的数据集
- 支持中文路径

### 3. 选择操作模式
程序提供6种操作模式：
1. **🎨 完整演示 (推荐)** - 处理3张图像，展示完整算法流程
2. **🔍 单张图像处理** - 选择特定图像进行处理
3. **🚀 批量处理所有图像** - 处理数据集中的所有图像
4. **🛠️ 图像加载测试** - 测试数据集图像加载功能
5. **📖 算法说明** - 查看详细的算法原理
6. **🚪 退出程序**

### 4. 自动处理和可视化
根据选择的模式，程序会：
- 应用红外图像增强算法
- 执行多尺度特征提取
- 进行注意力增强处理
- 执行目标检测
- 生成可视化结果和统计报告

### 5. 查看结果
结果保存在 `result/` 目录下：

#### 演示模式结果
- `demo_result_1.png` - 第一张图像的完整处理流程
- `demo_result_2.png` - 第二张图像的完整处理流程
- `demo_result_3.png` - 第三张图像的完整处理流程

#### 批量处理结果
- `batch_processing/` - 批量处理结果目录
  - `batch_processing_report.txt` - 详细统计报告
  - `batch_statistics.png` - 统计图表
  - `batch_result_*.png` - 部分图像的可视化结果

## 🎨 可视化内容

每个结果图像包含6个子图：

### 第一行
1. **原始图像** - 输入的红外图像
2. **红外增强后** - 应用对比度增强、边缘增强、噪声抑制后的图像
3. **多尺度特征** - 融合多尺度卷积特征的热力图

### 第二行
4. **注意力增强特征** - 应用注意力机制后的特征热力图
5. **检测结果** - 算法检测到的目标（绿色框）
6. **真实标签** - 数据集中的真实标签（红色框）

## 🔧 算法原理

### 1. 红外图像增强
```python
# 对比度增强 - 使用CLAHE算法
clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))

# 边缘增强 - 使用Laplacian算子
laplacian = cv2.Laplacian(gray, cv2.CV_64F)

# 噪声抑制 - 使用双边滤波
denoised = cv2.bilateralFilter(image, 9, 75, 75)
```

### 2. 多尺度特征融合
```python
# 模拟不同尺度的卷积核
feat_1x1 = point_feature_extraction(image)      # 点特征
feat_3x3 = sobel_edge_detection(image)          # 边缘特征  
feat_5x5 = gaussian_blur_feature(image)         # 平滑特征
feat_dilated = dilated_convolution(image)       # 大感受野特征

# 特征融合
fused = (feat_1x1 + feat_3x3 + feat_5x5 + feat_dilated) / 4
```

### 3. 注意力机制
```python
# 通道注意力 - 基于统计特征
attention_weight = (mean_value + max_value) / (2 * 255.0)

# 空间注意力 - 基于高斯权重
attention_map = cv2.GaussianBlur(features, (7,7), 1.0)
enhanced = features * attention_map
```

### 4. 目标检测
```python
# 阈值分割
_, binary = cv2.threshold(features, 0, 255, cv2.THRESH_OTSU)

# 形态学操作
kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3,3))
cleaned = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

# 轮廓检测
contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
```

## 📊 性能特点

### 优势
- ✅ **易于理解**: 使用传统图像处理，算法透明
- ✅ **快速部署**: 无需复杂的深度学习环境
- ✅ **实时处理**: 处理速度快，适合演示
- ✅ **可视化完整**: 每个步骤都有可视化展示

### 局限性
- ❌ **精度有限**: 传统方法精度不如深度学习
- ❌ **参数固定**: 无法自适应学习最优参数
- ❌ **泛化能力**: 对不同场景的适应性有限

## 🔍 故障排除

### 常见问题

1. **图像加载失败**
   ```
   解决方案: 运行 python test_image_loading.py 诊断问题
   ```

2. **中文路径问题**
   ```
   解决方案: 程序已自动处理中文路径，使用PIL加载
   ```

3. **依赖包缺失**
   ```bash
   pip install numpy matplotlib opencv-python pillow tqdm
   ```

4. **内存不足**
   ```
   解决方案: 关闭其他程序，或使用较小的图像
   ```

### 调试工具

使用图像加载测试工具：
```bash
python test_image_loading.py
```

该工具会：
- 测试多种图像加载方法
- 诊断路径和格式问题
- 生成测试结果图像

## 🚀 批量处理功能

### 处理选项
1. **仅生成检测统计** - 快速处理，只生成统计报告
2. **生成详细可视化结果** - 较慢，但包含部分图像的可视化

### 统计报告内容
- **处理统计**: 总图像数、成功率、失败数量
- **检测统计**: 总检测目标数、平均检测数、分布情况
- **性能分析**: 最小值、最大值、中位数、标准差
- **统计图表**: 检测数量分布、趋势分析、成功率饼图

### 注意事项
- 大数据集处理时间较长，建议先用演示模式测试
- 可视化模式会生成部分图像结果，避免文件过多
- 每100张图像显示一次进度报告
- 所有结果自动保存到 `result/batch_processing/` 目录

## 📈 示例结果

### 单张图像处理效果
- **检测目标数**: 200-500个候选区域
- **真实目标数**: 通常1-3个飞机目标
- **处理时间**: 每张图像约2-5秒
- **可视化质量**: 高清晰度，便于分析

### 批量处理效果
- **处理速度**: 每张图像约1-3秒
- **成功率**: 通常>95%
- **统计精度**: 完整的数据集统计分析
- **报告质量**: 详细的文本和图表报告

## 🎉 总结

本演示系统成功展示了：

1. **算法创新思路**: 基于FCOS、GFL、TOOD的改进方案
2. **完整处理流程**: 从图像增强到目标检测的全过程
3. **可视化效果**: 直观展示每个处理步骤的效果
4. **实用性**: 无需深度学习环境即可体验先进算法

虽然使用传统方法模拟，但很好地展示了深度学习算法的核心思想和创新点，为理解和学习现代目标检测算法提供了有价值的参考。
