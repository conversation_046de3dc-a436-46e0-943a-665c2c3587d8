@echo off
chcp 65001 >nul
title War Thunder数据集管理工具

echo.
echo ========================================
echo    War Thunder数据集管理工具
echo ========================================
echo.

REM 检查WSL是否可用
wsl --list --quiet >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未检测到WSL环境
    echo 请确保已安装并配置WSL
    pause
    exit /b 1
)

echo 🚀 正在启动工具...
echo.

REM 获取当前目录的Windows路径并转换为WSL路径
set "current_dir=%cd%"
echo 当前目录: %current_dir%

REM 使用wsl转换路径并运行Python脚本
wsl bash -c "cd $(wslpath '%current_dir%') && python quick_start.py"

echo.
echo 🎉 工具执行完成
pause 