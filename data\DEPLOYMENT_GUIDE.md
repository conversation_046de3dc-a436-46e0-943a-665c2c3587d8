# War Thunder数据集管理工具 - 部署指南

## 📋 概述
本工具用于管理War Thunder游戏数据集，包括文件质量检查、重命名、BBox过滤和可视化功能。

## 🚀 快速启动（推荐）

### Windows用户
1. **双击运行**：`快速启动.bat`
   - 自动检测Python环境
   - 如果没有Python，会尝试使用WSL
   - 无需配置路径，在任何目录都能工作

### Linux/macOS用户
1. **运行脚本**：`./快速启动.sh`
   - 自动检测python3或python
   - 跨平台兼容

### 通用方式
如果上述方式不工作，可以直接运行：
```bash
python quick_start.py
```

## 📁 文件结构
```
WT_Dataset_Tools/
├── quick_start.py              # 主启动脚本（交互式）
├── wt_dataset_manager.py       # 核心管理工具
├── universal_file_utils.py     # 通用文件工具
├── visualize_bbox_fixed.py     # 可视化工具
├── debug_bbox.py              # 调试工具
├── test_compatibility.py       # 兼容性测试
├── 快速启动.bat               # Windows启动脚本
├── 快速启动.sh                # Linux/macOS启动脚本
├── start_wt_tool.bat          # 备用启动脚本(WSL)
└── 文档/
    ├── DEPLOYMENT_GUIDE.md     # 部署指南
    ├── COMPATIBILITY_GUIDE.md  # 兼容性指南
    └── USAGE_GUIDE.md         # 使用指南
```

## 🔧 环境要求

### 最低要求
- Python 3.6+（推荐3.8+）
- 必需的Python包：
  - json（标准库）
  - os, sys, shutil（标准库）
  - PIL (Pillow) - 用于图像处理
  - matplotlib - 用于可视化

### 安装依赖
```bash
pip install pillow matplotlib
```

### 环境兼容性
**Windows用户有三种选择：**
1. **直接Python**：安装Python并添加到PATH（推荐）
2. **WSL环境**：在WSL中运行Linux版本
3. **Anaconda/Miniconda**：在conda环境中运行

**Linux/macOS用户：**
- 确保安装了python3或python
- Ubuntu/Debian: `sudo apt install python3 python3-pip`
- macOS: `brew install python3`

## 📂 数据集目录要求

### 标准目录结构
```
数据集目录/
├── output_images001.png
├── output_images002.png
├── video_label001.json
├── video_label002.json
└── ...
```

### 重命名后结构
```
数据集目录/
├── batch1_student01_001.png
├── batch1_student01_002.png
├── batch1_student01_001.json
├── batch1_student01_002.json
└── ...
```

## 🎯 核心功能

### 1. 数据集质量评估
- ✅ 检查图像-标签文件配对
- ✅ 验证JSON文件格式
- ✅ 筛选有效目标（isvisible=true且包含飞行模型）
- ✅ BBox坐标范围验证
- ✅ 删除无效文件

### 2. 文件重命名
- ✅ 批次化重命名：`batch{批次}_{学号}_{序号}`
- ✅ 自动配对图像和标签文件
- ✅ 支持自定义批次号和学号

### 3. 数据过滤
- ✅ 按BBox大小过滤目标
- ✅ 保留<90像素的小目标
- ✅ 删除≥90像素的大目标
- ✅ 统计报告：<10px, 10-33px, 33-90px, >90px

### 4. 可视化工具
- ✅ 随机选择不同大小目标
- ✅ BBox边框绘制
- ✅ 目标信息标注
- ✅ 保存可视化结果

## 🛡️ 兼容性保证

### 重命名前后兼容
- ✅ 工具自动检测文件格式
- ✅ 支持原始格式：`output_images*.png`
- ✅ 支持重命名格式：`batch*_*_*.png`
- ✅ 无缝切换，无需手动配置

### 错误处理
- ✅ 文件不存在时的降级处理
- ✅ 格式不匹配时的自动适配
- ✅ 详细的错误提示和解决建议

## 📝 使用流程

### 第一次使用
1. 将工具包解压到任意目录
2. 双击`快速启动.bat`（Windows）或运行`./快速启动.sh`（Linux/macOS）
3. 按提示输入数据集路径
4. 选择要执行的功能

### 常规工作流程
1. **数据评估** → 2. **文件重命名** → 3. **数据过滤** → 4. **质量检查** → 5. **可视化验证**

## 🚨 注意事项

### 数据安全
- ⚠️ 重命名和过滤操作会永久修改文件
- ✅ 建议先在备份数据上测试
- ✅ 工具会在操作前显示预览和确认

### 路径要求
- ✅ 支持中文路径
- ✅ 支持空格路径
- ✅ 自动处理路径分隔符

### 性能考虑
- 📊 大数据集（>1000文件）处理时间约1-5分钟
- 💾 建议确保足够的磁盘空间
- 🔧 可视化功能占用较多内存

## 🆘 故障排除

### 常见问题

**Q: 双击启动脚本没反应？**
A: 
1. 检查是否安装了Python
2. 尝试在命令行中运行`python quick_start.py`
3. 查看是否有错误提示

**Q: 提示找不到模块？**
A: 安装缺失的依赖：`pip install pillow matplotlib`

**Q: WSL相关错误？**
A: 
1. 确保WSL已正确安装
2. 在WSL中安装python3：`sudo apt install python3`
3. 或使用Windows本地Python环境

**Q: 文件路径错误？**
A: 
1. 确保数据集目录存在
2. 使用绝对路径
3. 检查路径中是否有特殊字符

**Q: 重命名后工具报错？**
A: 本版本已修复兼容性问题，如仍有问题请检查工具版本

### 获取帮助
- 📖 查看详细文档：`USAGE_GUIDE.md`
- 🔧 运行测试：`python test_compatibility.py`
- 🐛 调试模式：`python debug_bbox.py`

## 📈 版本信息
- **当前版本**: v2.0
- **更新日期**: 2024-01-XX
- **主要特性**: 
  - 通用环境兼容
  - 自动路径检测
  - 重命名前后完全兼容
  - 增强的错误处理

## 📞 技术支持
如遇到问题，请提供：
1. 操作系统版本
2. Python版本
3. 错误截图或日志
4. 数据集基本信息（文件数量、大小等） 