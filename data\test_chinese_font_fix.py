#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中文字体修复效果的脚本
"""

import os
import sys

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wt_dataset_manager import WTDatasetManager

def test_chinese_font_visualization():
    """测试中文字体可视化效果"""
    print("🧪 测试中文字体修复效果")
    print("=" * 50)
    
    # 获取数据集路径
    dataset_path = input("请输入数据集路径 (默认: D:\\WTdataset): ").strip()
    if not dataset_path:
        dataset_path = "D:\\WTdataset"
    
    dataset_path = os.path.normpath(dataset_path)
    
    if not os.path.exists(dataset_path):
        print(f"❌ 路径不存在: {dataset_path}")
        return
    
    print(f"📁 使用数据集路径: {dataset_path}")
    
    # 创建管理器实例
    print("\n🔧 初始化数据集管理器...")
    manager = WTDatasetManager(dataset_path)
    
    # 测试可视化功能
    print("\n🎨 开始生成BBox可视化 (测试中文字体)...")
    try:
        manager.visualize_bbox_samples(num_samples=3)  # 少量样本用于测试
        print("\n✅ 可视化完成!")
        print(f"📁 请检查输出目录: {os.path.join(dataset_path, 'bbox_visualization')}")
        print("📋 检查要点:")
        print("   1. 图像标题是否正确显示中文")
        print("   2. 飞机名称标签是否正确显示中文")
        print("   3. 总览图标题是否正确显示中文")
        
    except Exception as e:
        print(f"❌ 可视化失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        test_chinese_font_visualization()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，测试结束!")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
