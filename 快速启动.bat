@echo off
chcp 65001 >nul
title War Thunder数据集管理工具

echo.
echo ========================================
echo    War Thunder数据集管理工具
echo ========================================
echo.

REM 首先尝试直接运行Python
echo 🔍 检测运行环境...

REM 检查是否有Python直接可用
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 检测到Python环境
    echo 🚀 正在启动工具...
    python quick_start.py
    goto :end
)

REM 如果没有Python，检查WSL
echo 📋 未检测到直接的Python环境，尝试WSL...
wsl --list --quiet >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未检测到Python或WSL环境
    echo.
    echo 请确保满足以下条件之一：
    echo 1. 已安装Python并添加到PATH
    echo 2. 已安装并配置WSL环境
    echo.
    pause
    exit /b 1
)

echo ✅ 检测到WSL环境
echo 🚀 正在通过WSL启动工具...

REM 获取当前目录并通过WSL运行
set "current_dir=%cd%"
wsl bash -c "cd $(wslpath '%current_dir%') && python3 quick_start.py"

:end
echo.
echo 🎉 工具执行完成
pause 